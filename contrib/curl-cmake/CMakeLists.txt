option (ENABLE_CURL "Enable curl" ${ENABLE_LIBRARIES})

if (NOT ENABLE_CURL)
    message(STATUS "Not using curl")
    return()
endif()

set (LIBRARY_DIR "${ClickHouse_SOURCE_DIR}/contrib/curl")

set (SRCS
    "${LIBRARY_DIR}/lib/altsvc.c"
    "${LIBRARY_DIR}/lib/amigaos.c"
    "${LIBRARY_DIR}/lib/asyn-ares.c"
    "${LIBRARY_DIR}/lib/asyn-base.c"
    "${LIBRARY_DIR}/lib/asyn-thrdd.c"
    "${LIBRARY_DIR}/lib/bufq.c"
    "${LIBRARY_DIR}/lib/bufref.c"
    "${LIBRARY_DIR}/lib/cf-h1-proxy.c"
    "${LIBRARY_DIR}/lib/cf-h2-proxy.c"
    "${LIBRARY_DIR}/lib/cf-haproxy.c"
    "${LIBRARY_DIR}/lib/cf-https-connect.c"
    "${LIBRARY_DIR}/lib/cf-socket.c"
    "${LIBRARY_DIR}/lib/cfilters.c"
    "${LIBRARY_DIR}/lib/conncache.c"
    "${LIBRARY_DIR}/lib/connect.c"
    "${LIBRARY_DIR}/lib/content_encoding.c"
    "${LIBRARY_DIR}/lib/cookie.c"
    "${LIBRARY_DIR}/lib/cshutdn.c"
    "${LIBRARY_DIR}/lib/curl_addrinfo.c"
    "${LIBRARY_DIR}/lib/curl_des.c"
    "${LIBRARY_DIR}/lib/curl_endian.c"
    "${LIBRARY_DIR}/lib/curl_fnmatch.c"
    "${LIBRARY_DIR}/lib/curl_get_line.c"
    "${LIBRARY_DIR}/lib/curl_gethostname.c"
    "${LIBRARY_DIR}/lib/curl_gssapi.c"
    "${LIBRARY_DIR}/lib/curl_memrchr.c"
    "${LIBRARY_DIR}/lib/curl_ntlm_core.c"
    "${LIBRARY_DIR}/lib/curl_range.c"
    "${LIBRARY_DIR}/lib/curl_rtmp.c"
    "${LIBRARY_DIR}/lib/curl_sasl.c"
    "${LIBRARY_DIR}/lib/curl_sha512_256.c"
    "${LIBRARY_DIR}/lib/curl_sspi.c"
    "${LIBRARY_DIR}/lib/curl_threads.c"
    "${LIBRARY_DIR}/lib/curl_trc.c"
    "${LIBRARY_DIR}/lib/cw-out.c"
    "${LIBRARY_DIR}/lib/cw-pause.c"
    "${LIBRARY_DIR}/lib/dict.c"
    "${LIBRARY_DIR}/lib/doh.c"
    "${LIBRARY_DIR}/lib/dynhds.c"
    "${LIBRARY_DIR}/lib/easy.c"
    "${LIBRARY_DIR}/lib/easygetopt.c"
    "${LIBRARY_DIR}/lib/easyoptions.c"
    "${LIBRARY_DIR}/lib/escape.c"
    "${LIBRARY_DIR}/lib/fake_addrinfo.c"
    "${LIBRARY_DIR}/lib/file.c"
    "${LIBRARY_DIR}/lib/fileinfo.c"
    "${LIBRARY_DIR}/lib/fopen.c"
    "${LIBRARY_DIR}/lib/formdata.c"
    "${LIBRARY_DIR}/lib/ftp.c"
    "${LIBRARY_DIR}/lib/ftplistparser.c"
    "${LIBRARY_DIR}/lib/getenv.c"
    "${LIBRARY_DIR}/lib/getinfo.c"
    "${LIBRARY_DIR}/lib/gopher.c"
    "${LIBRARY_DIR}/lib/hash.c"
    "${LIBRARY_DIR}/lib/headers.c"
    "${LIBRARY_DIR}/lib/hmac.c"
    "${LIBRARY_DIR}/lib/hostip.c"
    "${LIBRARY_DIR}/lib/hostip4.c"
    "${LIBRARY_DIR}/lib/hostip6.c"
    "${LIBRARY_DIR}/lib/hsts.c"
    "${LIBRARY_DIR}/lib/http.c"
    "${LIBRARY_DIR}/lib/http1.c"
    "${LIBRARY_DIR}/lib/http2.c"
    "${LIBRARY_DIR}/lib/http_aws_sigv4.c"
    "${LIBRARY_DIR}/lib/http_chunks.c"
    "${LIBRARY_DIR}/lib/http_digest.c"
    "${LIBRARY_DIR}/lib/http_negotiate.c"
    "${LIBRARY_DIR}/lib/http_ntlm.c"
    "${LIBRARY_DIR}/lib/http_proxy.c"
    "${LIBRARY_DIR}/lib/httpsrr.c"
    "${LIBRARY_DIR}/lib/idn.c"
    "${LIBRARY_DIR}/lib/if2ip.c"
    "${LIBRARY_DIR}/lib/imap.c"
    "${LIBRARY_DIR}/lib/inet_ntop.c"
    "${LIBRARY_DIR}/lib/krb5.c"
    "${LIBRARY_DIR}/lib/ldap.c"
    "${LIBRARY_DIR}/lib/llist.c"
    "${LIBRARY_DIR}/lib/macos.c"
    "${LIBRARY_DIR}/lib/md4.c"
    "${LIBRARY_DIR}/lib/md5.c"
    "${LIBRARY_DIR}/lib/memdebug.c"
    "${LIBRARY_DIR}/lib/mime.c"
    "${LIBRARY_DIR}/lib/mprintf.c"
    "${LIBRARY_DIR}/lib/mqtt.c"
    "${LIBRARY_DIR}/lib/multi.c"
    "${LIBRARY_DIR}/lib/multi_ev.c"
    "${LIBRARY_DIR}/lib/netrc.c"
    "${LIBRARY_DIR}/lib/noproxy.c"
    "${LIBRARY_DIR}/lib/openldap.c"
    "${LIBRARY_DIR}/lib/parsedate.c"
    "${LIBRARY_DIR}/lib/pingpong.c"
    "${LIBRARY_DIR}/lib/pop3.c"
    "${LIBRARY_DIR}/lib/progress.c"
    "${LIBRARY_DIR}/lib/psl.c"
    "${LIBRARY_DIR}/lib/rand.c"
    "${LIBRARY_DIR}/lib/rename.c"
    "${LIBRARY_DIR}/lib/request.c"
    "${LIBRARY_DIR}/lib/rtsp.c"
    "${LIBRARY_DIR}/lib/select.c"
    "${LIBRARY_DIR}/lib/sendf.c"
    "${LIBRARY_DIR}/lib/setopt.c"
    "${LIBRARY_DIR}/lib/sha256.c"
    "${LIBRARY_DIR}/lib/share.c"
    "${LIBRARY_DIR}/lib/slist.c"
    "${LIBRARY_DIR}/lib/smb.c"
    "${LIBRARY_DIR}/lib/smtp.c"
    "${LIBRARY_DIR}/lib/socketpair.c"
    "${LIBRARY_DIR}/lib/socks.c"
    "${LIBRARY_DIR}/lib/socks_gssapi.c"
    "${LIBRARY_DIR}/lib/socks_sspi.c"
    "${LIBRARY_DIR}/lib/speedcheck.c"
    "${LIBRARY_DIR}/lib/splay.c"
    "${LIBRARY_DIR}/lib/strcase.c"
    "${LIBRARY_DIR}/lib/strdup.c"
    "${LIBRARY_DIR}/lib/strequal.c"
    "${LIBRARY_DIR}/lib/strerror.c"
    "${LIBRARY_DIR}/lib/system_win32.c"
    "${LIBRARY_DIR}/lib/telnet.c"
    "${LIBRARY_DIR}/lib/tftp.c"
    "${LIBRARY_DIR}/lib/transfer.c"
    "${LIBRARY_DIR}/lib/uint-bset.c"
    "${LIBRARY_DIR}/lib/uint-hash.c"
    "${LIBRARY_DIR}/lib/uint-spbset.c"
    "${LIBRARY_DIR}/lib/uint-table.c"
    "${LIBRARY_DIR}/lib/url.c"
    "${LIBRARY_DIR}/lib/urlapi.c"
    "${LIBRARY_DIR}/lib/version.c"
    "${LIBRARY_DIR}/lib/ws.c"
    "${LIBRARY_DIR}/lib/vauth/cleartext.c"
    "${LIBRARY_DIR}/lib/vauth/cram.c"
    "${LIBRARY_DIR}/lib/vauth/digest.c"
    "${LIBRARY_DIR}/lib/vauth/digest_sspi.c"
    "${LIBRARY_DIR}/lib/vauth/gsasl.c"
    "${LIBRARY_DIR}/lib/vauth/krb5_gssapi.c"
    "${LIBRARY_DIR}/lib/vauth/krb5_sspi.c"
    "${LIBRARY_DIR}/lib/vauth/ntlm.c"
    "${LIBRARY_DIR}/lib/vauth/ntlm_sspi.c"
    "${LIBRARY_DIR}/lib/vauth/oauth2.c"
    "${LIBRARY_DIR}/lib/vauth/spnego_gssapi.c"
    "${LIBRARY_DIR}/lib/vauth/spnego_sspi.c"
    "${LIBRARY_DIR}/lib/vauth/vauth.c"    
    "${LIBRARY_DIR}/lib/vquic/vquic.c"
    "${LIBRARY_DIR}/lib/vssh/curl_path.c"
    "${LIBRARY_DIR}/lib/vssh/libssh.c"
    "${LIBRARY_DIR}/lib/vssh/libssh2.c"

    "${LIBRARY_DIR}/lib/vtls/bearssl.c"
    "${LIBRARY_DIR}/lib/vtls/cipher_suite.c"
    "${LIBRARY_DIR}/lib/vtls/gtls.c"
    "${LIBRARY_DIR}/lib/vtls/hostcheck.c"
    "${LIBRARY_DIR}/lib/vtls/keylog.c"
    "${LIBRARY_DIR}/lib/vtls/mbedtls.c"
    "${LIBRARY_DIR}/lib/vtls/mbedtls_threadlock.c"
    "${LIBRARY_DIR}/lib/vtls/openssl.c"
    "${LIBRARY_DIR}/lib/vtls/rustls.c"
    "${LIBRARY_DIR}/lib/vtls/schannel.c"
    "${LIBRARY_DIR}/lib/vtls/schannel_verify.c"
    "${LIBRARY_DIR}/lib/vtls/sectransp.c"
    "${LIBRARY_DIR}/lib/vtls/vtls.c"
    "${LIBRARY_DIR}/lib/vtls/vtls_scache.c"
    "${LIBRARY_DIR}/lib/vtls/vtls_spack.c"
    "${LIBRARY_DIR}/lib/vtls/wolfssl.c"
    "${LIBRARY_DIR}/lib/vtls/x509asn1.c"
    "${LIBRARY_DIR}/lib/curlx/base64.c"
    "${LIBRARY_DIR}/lib/curlx/dynbuf.c"
    "${LIBRARY_DIR}/lib/curlx/inet_pton.c"
    "${LIBRARY_DIR}/lib/curlx/multibyte.c"
    "${LIBRARY_DIR}/lib/curlx/nonblock.c"
    "${LIBRARY_DIR}/lib/curlx/strparse.c"
    "${LIBRARY_DIR}/lib/curlx/timediff.c"
    "${LIBRARY_DIR}/lib/curlx/timeval.c"
    "${LIBRARY_DIR}/lib/curlx/version_win32.c"
    "${LIBRARY_DIR}/lib/curlx/warnless.c"
    "${LIBRARY_DIR}/lib/curlx/winapi.c"
)

add_library (_curl ${SRCS})

target_compile_definitions (_curl PRIVATE
    HAVE_CONFIG_H
    BUILDING_LIBCURL
    CURL_HIDDEN_SYMBOLS
    libcurl_EXPORTS
    OPENSSL_NO_ENGINE
    CURL_OS="${CMAKE_SYSTEM_NAME}"
)

target_include_directories (_curl SYSTEM PUBLIC
    "${LIBRARY_DIR}/include"
    "${LIBRARY_DIR}/lib"
    . # curl_config.h
)

target_link_libraries (_curl PRIVATE OpenSSL::SSL ch_contrib::c-ares)

# The library is large - avoid bloat (XXX: is it?)
if (OMIT_HEAVY_DEBUG_SYMBOLS)
    target_compile_options (_curl PRIVATE -g0)
endif()

add_library (ch_contrib::curl ALIAS _curl)
