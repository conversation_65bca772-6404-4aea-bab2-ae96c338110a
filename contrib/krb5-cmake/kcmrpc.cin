/*
 * IDENTIFICATION:
 * stub generated by bootstrap_cmds-129
 * OPTIONS: 
 */
#define	__MIG_check__Reply__mheim_ipc_subsystem__ 1

#include "kcmrpc.h"

/* TODO: #include <mach/mach.h> */
#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */
extern void mach_msg_destroy(mach_msg_header_t *);
#ifdef __cplusplus
}
#endif /* __cplusplus */

#ifndef	mig_internal
#define	mig_internal	static __inline__
#endif	/* mig_internal */

#ifndef	mig_external
#define mig_external
#endif	/* mig_external */

#if	!defined(__MigTypeCheck) && defined(TypeCheck)
#define	__MigTypeCheck		TypeCheck	/* Legacy setting */
#endif	/* !defined(__MigTypeCheck) */

#if	!defined(__MigKernelSpecificCode) && defined(_MIG_KERNEL_SPECIFIC_CODE_)
#define	__MigKernelSpecificCode	_MIG_KERNEL_SPECIFIC_CODE_	/* Legacy setting */
#endif	/* !defined(__MigKernelSpecificCode) */

#ifndef	LimitCheck
#define	LimitCheck 0
#endif	/* LimitCheck */

#ifndef	min
#define	min(a,b)  ( ((a) < (b))? (a): (b) )
#endif	/* min */

#if !defined(_WALIGN_)
#define _WALIGN_(x) (((x) + 3) & ~3)
#endif /* !defined(_WALIGN_) */

#if !defined(_WALIGNSZ_)
#define _WALIGNSZ_(x) _WALIGN_(sizeof(x))
#endif /* !defined(_WALIGNSZ_) */

#ifndef	UseStaticTemplates
#define	UseStaticTemplates	0
#endif	/* UseStaticTemplates */

#ifndef MIG_SERVER_ROUTINE
#define MIG_SERVER_ROUTINE
#endif

#ifndef	__MachMsgErrorWithTimeout
#define	__MachMsgErrorWithTimeout(_R_) { \
	switch (_R_) { \
	case MACH_SEND_INVALID_DATA: \
	case MACH_SEND_INVALID_DEST: \
	case MACH_SEND_INVALID_HEADER: \
		mig_put_reply_port(InP->Head.msgh_reply_port); \
		break; \
	case MACH_SEND_TIMED_OUT: \
	case MACH_RCV_TIMED_OUT: \
	default: \
		mig_dealloc_reply_port(InP->Head.msgh_reply_port); \
	} \
}
#endif	/* __MachMsgErrorWithTimeout */

#ifndef	__MachMsgErrorWithoutTimeout
#define	__MachMsgErrorWithoutTimeout(_R_) { \
	switch (_R_) { \
	case MACH_SEND_INVALID_DATA: \
	case MACH_SEND_INVALID_DEST: \
	case MACH_SEND_INVALID_HEADER: \
		mig_put_reply_port(InP->Head.msgh_reply_port); \
		break; \
	default: \
		mig_dealloc_reply_port(InP->Head.msgh_reply_port); \
	} \
}
#endif	/* __MachMsgErrorWithoutTimeout */

#ifndef	__DeclareSendRpc
#define	__DeclareSendRpc(_NUM_, _NAME_)
#endif	/* __DeclareSendRpc */

#ifndef	__BeforeSendRpc
#define	__BeforeSendRpc(_NUM_, _NAME_)
#endif	/* __BeforeSendRpc */

#ifndef	__AfterSendRpc
#define	__AfterSendRpc(_NUM_, _NAME_)
#endif	/* __AfterSendRpc */

#ifndef	__DeclareSendSimple
#define	__DeclareSendSimple(_NUM_, _NAME_)
#endif	/* __DeclareSendSimple */

#ifndef	__BeforeSendSimple
#define	__BeforeSendSimple(_NUM_, _NAME_)
#endif	/* __BeforeSendSimple */

#ifndef	__AfterSendSimple
#define	__AfterSendSimple(_NUM_, _NAME_)
#endif	/* __AfterSendSimple */

#define msgh_request_port	msgh_remote_port
#define msgh_reply_port		msgh_local_port



#if ( __MigTypeCheck )
#if __MIG_check__Reply__mheim_ipc_subsystem__
#if !defined(__MIG_check__Reply__call_t__defined)
#define __MIG_check__Reply__call_t__defined

mig_internal kern_return_t __MIG_check__Reply__call_t(__Reply__call_t *Out0P, __Reply__call_t **Out1PP)
{

	typedef __Reply__call_t __Reply __attribute__((unused));
	__Reply *Out1P;
	boolean_t msgh_simple;
#if	__MigTypeCheck
	unsigned int msgh_size;
#endif	/* __MigTypeCheck */
	unsigned int msgh_size_delta;

	if (Out0P->Head.msgh_id != 101) {
	    if (Out0P->Head.msgh_id == MACH_NOTIFY_SEND_ONCE)
		{ return MIG_SERVER_DIED; }
	    else
		{ return MIG_REPLY_MISMATCH; }
	}

	msgh_simple = !(Out0P->Head.msgh_bits & MACH_MSGH_BITS_COMPLEX);
#if	__MigTypeCheck
	msgh_size = Out0P->Head.msgh_size;

	if ((msgh_simple || Out0P->msgh_body.msgh_descriptor_count != 1 ||
	    msgh_size < (mach_msg_size_t)(sizeof(__Reply) - 2048) || msgh_size > (mach_msg_size_t)sizeof(__Reply)) &&
	    (!msgh_simple || msgh_size != (mach_msg_size_t)sizeof(mig_reply_error_t) ||
	    ((mig_reply_error_t *)Out0P)->RetCode == KERN_SUCCESS))
		{ return MIG_TYPE_ERROR ; }
#endif	/* __MigTypeCheck */

#if	__MigTypeCheck
	if (Out0P->Head.msgh_request_port != MACH_PORT_NULL) {
		return MIG_TYPE_ERROR;
	}
#endif	/* __MigTypeCheck */
	if (msgh_simple) {
		return ((mig_reply_error_t *)Out0P)->RetCode;
	}

#if	__MigTypeCheck
	if (Out0P->replyout.type != MACH_MSG_OOL_DESCRIPTOR) {
		return MIG_TYPE_ERROR;
	}
#endif	/* __MigTypeCheck */

	msgh_size_delta = _WALIGN_(Out0P->replyinCnt);
#if	__MigTypeCheck
	if ( Out0P->replyinCnt > 2048 )
		return MIG_TYPE_ERROR;
	if (((msgh_size - (mach_msg_size_t)(sizeof(__Reply) - 2048))< Out0P->replyinCnt) ||
	    (msgh_size != (mach_msg_size_t)(sizeof(__Reply) - 2048) + _WALIGN_(Out0P->replyinCnt)))
		{ return MIG_TYPE_ERROR ; }
#endif	/* __MigTypeCheck */

	*Out1PP = Out1P = (__Reply *) ((pointer_t) Out0P + msgh_size_delta - 2048);

#if __MigTypeCheck
	if (Out0P->replyout.size != Out1P->replyoutCnt)
		return MIG_TYPE_ERROR;
#endif	/* __MigTypeCheck */

	return MACH_MSG_SUCCESS;
}
#endif /* !defined(__MIG_check__Reply__call_t__defined) */
#endif /* __MIG_check__Reply__mheim_ipc_subsystem__ */
#endif /* ( __MigTypeCheck ) */


/* Routine call */
mig_external kern_return_t k5_kcmrpc_call
(
	mach_port_t server_port,
	k5_kcm_inband_msg requestin,
	mach_msg_type_number_t requestinCnt,
	k5_kcm_outband_msg requestout,
	mach_msg_type_number_t requestoutCnt,
	int *returnvalue,
	k5_kcm_inband_msg replyin,
	mach_msg_type_number_t *replyinCnt,
	k5_kcm_outband_msg *replyout,
	mach_msg_type_number_t *replyoutCnt
)
{

#ifdef  __MigPackStructs
#pragma pack(push, 4)
#endif
	typedef struct {
		mach_msg_header_t Head;
		/* start of the kernel processed data */
		mach_msg_body_t msgh_body;
		mach_msg_ool_descriptor_t requestout;
		/* end of the kernel processed data */
		NDR_record_t NDR;
		mach_msg_type_number_t requestinCnt;
		char requestin[2048];
		mach_msg_type_number_t requestoutCnt;
	} Request __attribute__((unused));
#ifdef  __MigPackStructs
#pragma pack(pop)
#endif

#ifdef  __MigPackStructs
#pragma pack(push, 4)
#endif
	typedef struct {
		mach_msg_header_t Head;
		/* start of the kernel processed data */
		mach_msg_body_t msgh_body;
		mach_msg_ool_descriptor_t replyout;
		/* end of the kernel processed data */
		NDR_record_t NDR;
		int returnvalue;
		mach_msg_type_number_t replyinCnt;
		char replyin[2048];
		mach_msg_type_number_t replyoutCnt;
		mach_msg_trailer_t trailer;
	} Reply __attribute__((unused));
#ifdef  __MigPackStructs
#pragma pack(pop)
#endif

#ifdef  __MigPackStructs
#pragma pack(push, 4)
#endif
	typedef struct {
		mach_msg_header_t Head;
		/* start of the kernel processed data */
		mach_msg_body_t msgh_body;
		mach_msg_ool_descriptor_t replyout;
		/* end of the kernel processed data */
		NDR_record_t NDR;
		int returnvalue;
		mach_msg_type_number_t replyinCnt;
		char replyin[2048];
		mach_msg_type_number_t replyoutCnt;
	} __Reply __attribute__((unused));
#ifdef  __MigPackStructs
#pragma pack(pop)
#endif
	/*
	 * typedef struct {
	 * 	mach_msg_header_t Head;
	 * 	NDR_record_t NDR;
	 * 	kern_return_t RetCode;
	 * } mig_reply_error_t;
	 */

	union {
		Request In;
		Reply Out;
	} Mess;

	Request *InP = &Mess.In;
	Reply *Out0P = &Mess.Out;
	Reply *Out1P = NULL;

	mach_msg_return_t msg_result;
	unsigned int msgh_size;
	unsigned int msgh_size_delta;


#ifdef	__MIG_check__Reply__call_t__defined
	kern_return_t check_result;
#endif	/* __MIG_check__Reply__call_t__defined */

	__DeclareSendRpc(1, "call")

#if	UseStaticTemplates
	const static mach_msg_ool_descriptor_t requestoutTemplate = {
		/* addr = */		(void *)0,
		/* size = */		0,
		/* deal = */		FALSE,
		/* copy = */		MACH_MSG_VIRTUAL_COPY,
		/* pad2 = */		0,
		/* type = */		MACH_MSG_OOL_DESCRIPTOR,
	};
#endif	/* UseStaticTemplates */

	InP->msgh_body.msgh_descriptor_count = 1;
#if	UseStaticTemplates
	InP->requestout = requestoutTemplate;
	InP->requestout.address = (void *)(requestout);
	InP->requestout.size = requestoutCnt;
#else	/* UseStaticTemplates */
	InP->requestout.address = (void *)(requestout);
	InP->requestout.size = requestoutCnt;
	InP->requestout.deallocate =  FALSE;
	InP->requestout.copy = MACH_MSG_VIRTUAL_COPY;
	InP->requestout.type = MACH_MSG_OOL_DESCRIPTOR;
#endif	/* UseStaticTemplates */

	InP->NDR = NDR_record;

	if (requestinCnt > 2048) {
		{ return MIG_ARRAY_TOO_LARGE; }
	}
	(void)memcpy((char *) InP->requestin, (const char *) requestin, requestinCnt);

	InP->requestinCnt = requestinCnt;

	msgh_size_delta = _WALIGN_(requestinCnt);
	msgh_size = (mach_msg_size_t)(sizeof(Request) - 2048) + msgh_size_delta;
	InP = (Request *) ((pointer_t) InP + msgh_size_delta - 2048);

	InP->requestoutCnt = requestoutCnt;

	InP = &Mess.In;
	InP->Head.msgh_reply_port = mig_get_reply_port();
	InP->Head.msgh_bits = MACH_MSGH_BITS_COMPLEX|
		MACH_MSGH_BITS(19, 21);
	/* msgh_size passed as argument */
	InP->Head.msgh_request_port = server_port;
	InP->Head.msgh_id = 1;
	InP->Head.msgh_reserved = 0;
	
/* BEGIN VOUCHER CODE */

#ifdef USING_VOUCHERS
	if (voucher_mach_msg_set != NULL) {
		voucher_mach_msg_set(&InP->Head);
	}
#endif // USING_VOUCHERS
	
/* END VOUCHER CODE */

	__BeforeSendRpc(1, "call")
	msg_result = mach_msg(&InP->Head, MACH_SEND_MSG|MACH_RCV_MSG|MACH_MSG_OPTION_NONE, msgh_size, (mach_msg_size_t)sizeof(Reply), InP->Head.msgh_reply_port, MACH_MSG_TIMEOUT_NONE, MACH_PORT_NULL);
	__AfterSendRpc(1, "call")
	if (msg_result != MACH_MSG_SUCCESS) {
		__MachMsgErrorWithoutTimeout(msg_result);
	}
	if (msg_result != MACH_MSG_SUCCESS) {
		{ return msg_result; }
	}


#if	defined(__MIG_check__Reply__call_t__defined)
	check_result = __MIG_check__Reply__call_t((__Reply__call_t *)Out0P, (__Reply__call_t **)&Out1P);
	if (check_result != MACH_MSG_SUCCESS) {
		mach_msg_destroy(&Out0P->Head);
		{ return check_result; }
	}
#endif	/* defined(__MIG_check__Reply__call_t__defined) */

	*returnvalue = Out0P->returnvalue;

	if (Out0P->replyinCnt > 2048) {
		(void)memcpy((char *) replyin, (const char *) Out0P->replyin,  2048);
		*replyinCnt = Out0P->replyinCnt;
		{ return MIG_ARRAY_TOO_LARGE; }
	}
	(void)memcpy((char *) replyin, (const char *) Out0P->replyin, Out0P->replyinCnt);

	*replyinCnt = Out0P->replyinCnt;

	*replyout = (k5_kcm_outband_msg)(Out0P->replyout.address);
	*replyoutCnt = Out1P->replyoutCnt;

	return KERN_SUCCESS;
}
