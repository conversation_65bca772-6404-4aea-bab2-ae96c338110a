#ifndef	_mheim_ipc_user_
#define	_mheim_ipc_user_

/* Module mheim_ipc */

#include <string.h>
#include <mach/ndr.h>
#include <mach/boolean.h>
#include <mach/kern_return.h>
#include <mach/notify.h>
#include <mach/mach_types.h>
#include <mach/message.h>
#include <mach/mig_errors.h>
#include <mach/port.h>
	
/* BEGIN VOUCHER CODE */

#ifndef KERNEL
#if defined(__has_include)
#if __has_include(<mach/mig_voucher_support.h>)
#ifndef USING_VOUCHERS
#define USING_VOUCHERS
#endif
#ifndef __VOUCHER_FORWARD_TYPE_DECLS__
#define __VOUCHER_FORWARD_TYPE_DECLS__
#ifdef __cplusplus
extern "C" {
#endif
#ifndef __VOUCHER_FOWARD_TYPE_DECLS_SINGLE_ATTR
#define __VOUCHER_FOWARD_TYPE_DECLS_SINGLE_ATTR __unsafe_indexable
#endif
	extern boolean_t voucher_mach_msg_set(mach_msg_header_t * msg) __attribute__((weak_import));
#ifdef __cplusplus
}
#endif
#endif // __VOUCHER_FORWARD_TYPE_DECLS__
#endif // __has_include(<mach/mach_voucher_types.h>)
#endif // __has_include
#endif // !KERNEL
	
/* END VOUCHER CODE */

	
/* BEGIN MIG_STRNCPY_ZEROFILL CODE */

#if defined(__has_include)
#if __has_include(<mach/mig_strncpy_zerofill_support.h>)
#ifndef USING_MIG_STRNCPY_ZEROFILL
#define USING_MIG_STRNCPY_ZEROFILL
#endif
#ifndef __MIG_STRNCPY_ZEROFILL_FORWARD_TYPE_DECLS__
#define __MIG_STRNCPY_ZEROFILL_FORWARD_TYPE_DECLS__
#ifdef __cplusplus
extern "C" {
#endif
#ifndef __MIG_STRNCPY_ZEROFILL_FORWARD_TYPE_DECLS_CSTRING_ATTR
#define __MIG_STRNCPY_ZEROFILL_FORWARD_TYPE_DECLS_CSTRING_COUNTEDBY_ATTR(C) __unsafe_indexable
#endif
	extern int mig_strncpy_zerofill(char * dest, const char * src, int len) __attribute__((weak_import));
#ifdef __cplusplus
}
#endif
#endif /* __MIG_STRNCPY_ZEROFILL_FORWARD_TYPE_DECLS__ */
#endif /* __has_include(<mach/mig_strncpy_zerofill_support.h>) */
#endif /* __has_include */
	
/* END MIG_STRNCPY_ZEROFILL CODE */


#ifdef AUTOTEST
#ifndef FUNCTION_PTR_T
#define FUNCTION_PTR_T
typedef void (*function_ptr_t)(mach_port_t, char *, mach_msg_type_number_t);
typedef struct {
        char            * name;
        function_ptr_t  function;
} function_table_entry;
typedef function_table_entry   *function_table_t;
#endif /* FUNCTION_PTR_T */
#endif /* AUTOTEST */

#ifndef	mheim_ipc_MSG_COUNT
#define	mheim_ipc_MSG_COUNT	1
#endif	/* mheim_ipc_MSG_COUNT */

#include <Availability.h>
#include <mach/std_types.h>
#include <mach/mig.h>
#include <mach/mig.h>
#include <mach/mach_types.h>
#include "kcmrpc_types.h"

#ifdef __BeforeMigUserHeader
__BeforeMigUserHeader
#endif /* __BeforeMigUserHeader */

#include <sys/cdefs.h>
__BEGIN_DECLS


/* Routine call */
#ifdef	mig_external
mig_external
#else
extern
#endif	/* mig_external */
kern_return_t k5_kcmrpc_call
(
	mach_port_t server_port,
	k5_kcm_inband_msg requestin,
	mach_msg_type_number_t requestinCnt,
	k5_kcm_outband_msg requestout,
	mach_msg_type_number_t requestoutCnt,
	int *returnvalue,
	k5_kcm_inband_msg replyin,
	mach_msg_type_number_t *replyinCnt,
	k5_kcm_outband_msg *replyout,
	mach_msg_type_number_t *replyoutCnt
);

__END_DECLS

/********************** Caution **************************/
/* The following data types should be used to calculate  */
/* maximum message sizes only. The actual message may be */
/* smaller, and the position of the arguments within the */
/* message layout may vary from what is presented here.  */
/* For example, if any of the arguments are variable-    */
/* sized, and less than the maximum is sent, the data    */
/* will be packed tight in the actual message to reduce  */
/* the presence of holes.                                */
/********************** Caution **************************/

/* typedefs for all requests */

#ifndef __Request__mheim_ipc_subsystem__defined
#define __Request__mheim_ipc_subsystem__defined

#ifdef  __MigPackStructs
#pragma pack(push, 4)
#endif
	typedef struct {
		mach_msg_header_t Head;
		/* start of the kernel processed data */
		mach_msg_body_t msgh_body;
		mach_msg_ool_descriptor_t requestout;
		/* end of the kernel processed data */
		NDR_record_t NDR;
		mach_msg_type_number_t requestinCnt;
		char requestin[2048];
		mach_msg_type_number_t requestoutCnt;
	} __Request__call_t __attribute__((unused));
#ifdef  __MigPackStructs
#pragma pack(pop)
#endif
#endif /* !__Request__mheim_ipc_subsystem__defined */

/* union of all requests */

#ifndef __RequestUnion__k5_kcmrpc_mheim_ipc_subsystem__defined
#define __RequestUnion__k5_kcmrpc_mheim_ipc_subsystem__defined
union __RequestUnion__k5_kcmrpc_mheim_ipc_subsystem {
	__Request__call_t Request_k5_kcmrpc_call;
};
#endif /* !__RequestUnion__k5_kcmrpc_mheim_ipc_subsystem__defined */
/* typedefs for all replies */

#ifndef __Reply__mheim_ipc_subsystem__defined
#define __Reply__mheim_ipc_subsystem__defined

#ifdef  __MigPackStructs
#pragma pack(push, 4)
#endif
	typedef struct {
		mach_msg_header_t Head;
		/* start of the kernel processed data */
		mach_msg_body_t msgh_body;
		mach_msg_ool_descriptor_t replyout;
		/* end of the kernel processed data */
		NDR_record_t NDR;
		int returnvalue;
		mach_msg_type_number_t replyinCnt;
		char replyin[2048];
		mach_msg_type_number_t replyoutCnt;
	} __Reply__call_t __attribute__((unused));
#ifdef  __MigPackStructs
#pragma pack(pop)
#endif
#endif /* !__Reply__mheim_ipc_subsystem__defined */

/* union of all replies */

#ifndef __ReplyUnion__k5_kcmrpc_mheim_ipc_subsystem__defined
#define __ReplyUnion__k5_kcmrpc_mheim_ipc_subsystem__defined
union __ReplyUnion__k5_kcmrpc_mheim_ipc_subsystem {
	__Reply__call_t Reply_k5_kcmrpc_call;
};
#endif /* !__RequestUnion__k5_kcmrpc_mheim_ipc_subsystem__defined */

#ifndef subsystem_to_name_map_mheim_ipc
#define subsystem_to_name_map_mheim_ipc \
    { "call", 1 }
#endif

#ifdef __AfterMigUserHeader
__AfterMigUserHeader
#endif /* __AfterMigUserHeader */

#endif	 /* _mheim_ipc_user_ */
