set(DELTA_KERNEL_RS_SOURCE_DIR "${ClickHouse_SOURCE_DIR}/contrib/delta-kernel-rs")
set(DELTA_KERNEL_RS_BINARY_DIR "${CMAKE_BINARY_DIR}/contrib/delta-kernel-rs")
set(DELTA_KERNEL_RS_CMAKE_DIR "${ClickHouse_SOURCE_DIR}/contrib/delta-kernel-rs-cmake")

include(ExternalProject)

set(USE_DELTA_KERNEL_RS ${ENABLE_LIBRARIES})

# MSAN: Disabled because ring does not generate the appropriate symbols
# NO_ARMV81_OR_HIGHER: Disabled because ring assumes that Neon is available with ARM
# Darwin: Issues with reqwest -> system-configuration (requires other frameworks)
if (NOT ENABLE_LIBRARIES OR NOT OS_LINUX OR (NOT ARCH_AMD64 AND NOT ARCH_AARCH64) OR (SANITIZE STREQUAL "memory") OR NO_ARMV81_OR_HIGHER)
  message(STATUS "Disabling delta kernel because of incompatible platform or Rust is disabled")
  set(USE_DELTA_KERNEL_RS 0)
endif()

option(ENABLE_DELTA_KERNEL_RS "Enable delta-kernel-rs" ${USE_DELTA_KERNEL_RS})
if (NOT ENABLE_DELTA_KERNEL_RS OR NOT ENABLE_RUST)
  message(STATUS "Not using delta-kernel-rs")
  return()
endif()

# Ideally we would disable all features to remove dependencies but:
# * default-engine: Needed for its s3 client (we can't pass our own s3 client via ffi)
clickhouse_import_crate(
    MANIFEST_PATH "${DELTA_KERNEL_RS_SOURCE_DIR}/ffi/Cargo.toml"
    FEATURES "default-engine, test-ffi"
)
clickhouse_config_crate_flags(delta_kernel_ffi)

# We use our own OpenSSL headers and libs
if(ARCH_AMD64)
    if(OS_DARWIN)
        set(PLATFORM_DIRECTORY darwin_x86_64)
    else()
        set(PLATFORM_DIRECTORY linux_x86_64)
    endif()
elseif(ARCH_AARCH64)
    if(OS_DARWIN)
        set(PLATFORM_DIRECTORY darwin_aarch64)
    else()
        set(PLATFORM_DIRECTORY linux_aarch64)
    endif()
elseif(ARCH_PPC64LE)
    set(PLATFORM_DIRECTORY linux_ppc64le)
elseif(ARCH_S390X)
    set(PLATFORM_DIRECTORY linux_s390x)
elseif(ARCH_RISCV64)
    set(PLATFORM_DIRECTORY linux_riscv64)
elseif(ARCH_LOONGARCH64)
    set(PLATFORM_DIRECTORY linux_loongarch64)
endif()

file(COPY ${ClickHouse_SOURCE_DIR}/contrib/openssl-cmake/${PLATFORM_DIRECTORY}/include/ DESTINATION ${DELTA_KERNEL_RS_BINARY_DIR}/${PLATFORM_DIRECTORY})
file(COPY ${ClickHouse_SOURCE_DIR}/contrib/openssl/include/ DESTINATION ${DELTA_KERNEL_RS_BINARY_DIR}/${PLATFORM_DIRECTORY})

# Path for autogenerated files (`delta_kernel_ffi.hpp`)
set(DELTA_KERNEL_TARGET_PATH ${DELTA_KERNEL_RS_BINARY_DIR}/include)

corrosion_set_env_vars(delta_kernel_ffi
        "CARGO_HOME=${CMAKE_BINARY_DIR}/contrib/corrosion-cmake/"
        "CARGO_TARGET_DIR=${DELTA_KERNEL_TARGET_PATH}"
        "OPENSSL_LIBS=ssl:crypto"
        "OPENSSL_STATIC=1"
        "OPENSSL_LIB_DIR=${CMAKE_BINARY_DIR}/contrib/openssl-cmake/"
        "OPENSSL_INCLUDE_DIR=${DELTA_KERNEL_RS_BINARY_DIR}/${PLATFORM_DIRECTORY}")

target_include_directories(delta_kernel_ffi INTERFACE "${DELTA_KERNEL_TARGET_PATH}/")
target_link_libraries(delta_kernel_ffi INTERFACE OpenSSL::Crypto OpenSSL::SSL)
add_dependencies(_cargo-build_delta_kernel_ffi OpenSSL::Crypto OpenSSL::SSL)
add_library(ch_contrib::delta_kernel_rs ALIAS delta_kernel_ffi)
