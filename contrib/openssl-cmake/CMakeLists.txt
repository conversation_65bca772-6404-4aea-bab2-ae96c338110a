cmake_minimum_required(VERSION 3.25)

# Actually, so many 3rd party libraries + unit tests need SSL that we cannot disable it
# without breaking the build ...
set(ENABLE_SSL 1 CACHE INTERNAL "")
# TODO: Making SSL dependent on ENABLE_LIBRARIES is desirable but needs fixing dependent libs + tests.
# option(ENABLE_SSL "Enable ssl" ${ENABLE_LIBRARIES})
if(NOT ENABLE_SSL)
  message(STATUS "Not using openssl")
  return()
endif()

# Below build description was generated from these steps:
# - Checkout OpenSSL in the desired version (e.g. 3.2)
# - Take a brief look (but not too long to save your mental sanity) at the supported build options (*)
# - Run `make clean && ./Configure enable-fips no-zlib no-ktls linux-x86_64 && make`
#   - enable-fips enables the FIPS provider
#   - no-zlib should disable the compression feature in OpenSSL, which is as far as I read (*) discouraged and deprecated
#   - no-ktls disables kernel-TLS, it is not clear to make that work in a portable way
#   - Perhaps more options need to be enabled or disabled, looking at (*), the defaults are not always clear and sometimes
#     options stop working in newer OpenSSL versions (i.e. the docs are outdated)
# - Then look at the beautiful Makefile with more than 37k LOC generated by a bunch of Perl scripts.
# - Search in the Makefile for stuff below. If you find something, check what are the differences. Then think about adding
#   or removing stuff in a reasonable way. Compile to check if it works, rinse and repeat.

# Platforms except linux_x86_64 and linux_aarch64 were compiled with 'no-asm' to keep the build descriptions simple.

# (*) https://github.com/openssl/openssl/blob/master/INSTALL.md

set(OPENSSL_SOURCE_DIR ${ClickHouse_SOURCE_DIR}/contrib/openssl)

set(OPENSSLDIR "/etc/ssl" CACHE PATH "Set the default openssl directory")
set(OPENSSL_ENGINESDIR "/usr/local/lib/engines-3" CACHE PATH "Set the default openssl directory for engines")
set(OPENSSL_MODULESDIR "/usr/local/lib/ossl-modules" CACHE PATH "Set the default openssl directory for modules")

# special type of build during cross-compilation
if(OPENSSL_AUX_BUILD_FOR_CROSS_COMPILATION)
    add_definitions(-DOPENSSL_NO_KTLS -DOPENSSLDIR="\\\"${OPENSSLDIR}\\\"" -DENGINESDIR="\\\"${OPENSSL_ENGINESDIR}\\\"" -DMODULESDIR="\\\"${OPENSSL_MODULESDIR}\\\"" -DOPENSSL_USE_NODELETE -DOPENSSL_PIC)
    add_compile_options("-Wno-deprecated-declarations")
    add_compile_options("-Wno-poison-system-directories")
else()
    add_definitions(-DOPENSSL_NO_KTLS -DOPENSSLDIR="${OPENSSLDIR}" -DENGINESDIR="${OPENSSL_ENGINESDIR}" -DMODULESDIR="${OPENSSL_MODULESDIR}" -DOPENSSL_USE_NODELETE -DOPENSSL_PIC)
    target_compile_options(global-group INTERFACE "-Wno-deprecated-declarations")
    target_compile_options(global-group INTERFACE "-Wno-poison-system-directories")
endif()

if(ARCH_AMD64)
    if(OS_DARWIN)
        set(PLATFORM_DIRECTORY darwin_x86_64)
        add_definitions(-DL_ENDIAN)
    else()
        set(PLATFORM_DIRECTORY linux_x86_64)
        add_definitions(-DAES_ASM -DBSAES_ASM -DCMLL_ASM -DECP_NISTZ256_ASM -DGHASH_ASM -DKECCAK1600_ASM -DMD5_ASM -DOPENSSL_BN_ASM_GF2m -DOPENSSL_BN_ASM_MONT -DOPENSSL_BN_ASM_MONT5 -DOPENSSL_CPUID_OBJ -DOPENSSL_IA32_SSE2 -DPOLY1305_ASM -DRC4_ASM -DSHA1_ASM -DSHA256_ASM -DSHA512_ASM -DVPAES_ASM -DWHIRLPOOL_ASM -DX25519_ASM -DL_ENDIAN)
    endif()
elseif(ARCH_AARCH64)
    if(OS_DARWIN)
        set(PLATFORM_DIRECTORY darwin_aarch64)
        add_definitions(-DL_ENDIAN)
    else()
        set(PLATFORM_DIRECTORY linux_aarch64)
        add_definitions(-DBSAES_ASM -DECP_NISTZ256_ASM -DECP_SM2P256_ASM -DKECCAK1600_ASM -DMD5_ASM -DOPENSSL_BN_ASM_MONT -DOPENSSL_CPUID_OBJ -DOPENSSL_SM3_ASM -DPOLY1305_ASM -DSHA1_ASM -DSHA256_ASM -DSHA512_ASM -DSM4_ASM -DVPAES_ASM -DVPSM4_ASM -DL_ENDIAN)
    endif()
elseif(ARCH_PPC64LE)
    set(PLATFORM_DIRECTORY linux_ppc64le)
    add_definitions(-DOPENSSL_CPUID_OBJ -DL_ENDIAN)
elseif(ARCH_S390X)
    set(PLATFORM_DIRECTORY linux_s390x)
    add_definitions(-DAES_CTR_ASM -DOPENSSL_CPUID_OBJ -DB_ENDIAN)
elseif(ARCH_RISCV64)
    set(PLATFORM_DIRECTORY linux_riscv64)
    add_definitions(-DOPENSSL_CPUID_OBJ -DL_ENDIAN)
elseif(ARCH_LOONGARCH64)
    set(PLATFORM_DIRECTORY linux_loongarch64)
    add_definitions(-DOPENSSL_CPUID_OBJ -DL_ENDIAN)
endif()

file(STRINGS "${PLATFORM_DIRECTORY}/include/openssl/opensslv.h" OPENSSL_VERSION_STR
    REGEX "^#[\t ]*define[\t ]+OPENSSL_VERSION_STR[\t ]+\"([0-9])+\\.([0-9])+\\.([0-9])+\".*")
string(REGEX REPLACE "^.*OPENSSL_VERSION_STR[\t ]+\"([0-9]+\\.[0-9]+\\.[0-9]+)\".*$"
    "\\1" OPENSSL_VERSION_STR "${OPENSSL_VERSION_STR}")

set(OPENSSL_VERSION "${OPENSSL_VERSION_STR}")

string(REGEX MATCHALL "([0-9])+" OPENSSL_VERSION_NUMBER "${OPENSSL_VERSION}")
list(POP_FRONT OPENSSL_VERSION_NUMBER
    OPENSSL_VERSION_MAJOR
    OPENSSL_VERSION_MINOR
    OPENSSL_VERSION_FIX)

unset(OPENSSL_VERSION_NUMBER)
unset(OPENSSL_VERSION_STR)

message(STATUS "OpenSSL version ${OPENSSL_VERSION}")

set(VERSION_MAJOR ${OPENSSL_VERSION_MAJOR})
set(VERSION_MINOR ${OPENSSL_VERSION_MINOR})
set(VERSION_PATCH ${OPENSSL_VERSION_FIX})

set_property(GLOBAL PROPERTY OPENSSL_VERSION_PROP "${OPENSSL_VERSION}")
set(VERSION_STRING ${OPENSSL_VERSION})
set(LIB_VERSION ${VERSION_MAJOR})
set(LIB_SOVERSION ${VERSION_MAJOR})

enable_language(ASM)

add_definitions(-Wno-unused-command-line-argument)
# Note that s390x build uses mold linker
if(NOT ARCH_S390X)
    set(CMAKE_SHARED_LINKER_FLAGS "${CMAKE_SHARED_LINKER_FLAGS} -fuse-ld=lld") # only relevant for -DENABLE_OPENSSL_DYNAMIC=1
endif()

set(CRYPTO_SRC
    der_digests_gen.c
    der_dsa_gen.c
    der_ec_gen.c
    der_ecx_gen.c
    der_rsa_gen.c
    der_wrap_gen.c
    der_sm2_gen.c

    ${PLATFORM_DIRECTORY}/params_idx.c

    ${OPENSSL_SOURCE_DIR}/crypto/aes/aes_cfb.c
    ${OPENSSL_SOURCE_DIR}/crypto/aes/aes_ecb.c
    ${OPENSSL_SOURCE_DIR}/crypto/aes/aes_ige.c
    ${OPENSSL_SOURCE_DIR}/crypto/aes/aes_misc.c
    ${OPENSSL_SOURCE_DIR}/crypto/aes/aes_ofb.c
    ${OPENSSL_SOURCE_DIR}/crypto/aes/aes_wrap.c
    ${OPENSSL_SOURCE_DIR}/crypto/aria/aria.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/a_bitstr.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/a_d2i_fp.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/a_digest.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/a_dup.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/a_gentm.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/a_i2d_fp.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/a_int.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/a_mbstr.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/a_object.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/a_octet.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/a_print.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/a_sign.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/a_strex.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/a_strnid.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/a_time.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/a_type.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/a_utctm.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/a_utf8.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/a_verify.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/ameth_lib.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/asn1_err.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/asn1_gen.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/asn1_item_list.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/asn1_lib.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/asn1_parse.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/asn_mime.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/asn_moid.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/asn_mstbl.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/asn_pack.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/bio_asn1.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/bio_ndef.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/d2i_param.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/d2i_pr.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/d2i_pu.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/evp_asn1.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/f_int.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/f_string.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/i2d_evp.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/n_pkey.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/nsseq.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/p5_pbe.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/p5_pbev2.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/p5_scrypt.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/p8_pkey.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/t_bitst.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/t_pkey.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/t_spki.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/tasn_dec.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/tasn_enc.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/tasn_fre.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/tasn_new.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/tasn_prn.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/tasn_scn.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/tasn_typ.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/tasn_utl.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/x_algor.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/x_bignum.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/x_info.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/x_int64.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/x_long.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/x_pkey.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/x_sig.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/x_spki.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1/x_val.c
    ${OPENSSL_SOURCE_DIR}/crypto/asn1_dsa.c
    ${OPENSSL_SOURCE_DIR}/crypto/async/arch/async_null.c
    ${OPENSSL_SOURCE_DIR}/crypto/async/arch/async_posix.c
    ${OPENSSL_SOURCE_DIR}/crypto/async/arch/async_win.c
    ${OPENSSL_SOURCE_DIR}/crypto/async/async.c
    ${OPENSSL_SOURCE_DIR}/crypto/async/async_err.c
    ${OPENSSL_SOURCE_DIR}/crypto/async/async_wait.c
    ${OPENSSL_SOURCE_DIR}/crypto/bf/bf_cfb64.c
    ${OPENSSL_SOURCE_DIR}/crypto/bf/bf_ecb.c
    ${OPENSSL_SOURCE_DIR}/crypto/bf/bf_enc.c
    ${OPENSSL_SOURCE_DIR}/crypto/bf/bf_ofb64.c
    ${OPENSSL_SOURCE_DIR}/crypto/bf/bf_skey.c
    ${OPENSSL_SOURCE_DIR}/crypto/bio/bf_buff.c
    ${OPENSSL_SOURCE_DIR}/crypto/bio/bf_lbuf.c
    ${OPENSSL_SOURCE_DIR}/crypto/bio/bf_nbio.c
    ${OPENSSL_SOURCE_DIR}/crypto/bio/bf_null.c
    ${OPENSSL_SOURCE_DIR}/crypto/bio/bf_prefix.c
    ${OPENSSL_SOURCE_DIR}/crypto/bio/bf_readbuff.c
    ${OPENSSL_SOURCE_DIR}/crypto/bio/bio_addr.c
    ${OPENSSL_SOURCE_DIR}/crypto/bio/bio_cb.c
    ${OPENSSL_SOURCE_DIR}/crypto/bio/bio_dump.c
    ${OPENSSL_SOURCE_DIR}/crypto/bio/bio_err.c
    ${OPENSSL_SOURCE_DIR}/crypto/bio/bio_lib.c
    ${OPENSSL_SOURCE_DIR}/crypto/bio/bio_meth.c
    ${OPENSSL_SOURCE_DIR}/crypto/bio/bio_print.c
    ${OPENSSL_SOURCE_DIR}/crypto/bio/bio_sock.c
    ${OPENSSL_SOURCE_DIR}/crypto/bio/bio_sock2.c
    ${OPENSSL_SOURCE_DIR}/crypto/bio/bss_acpt.c
    ${OPENSSL_SOURCE_DIR}/crypto/bio/bss_bio.c
    ${OPENSSL_SOURCE_DIR}/crypto/bio/bss_conn.c
    ${OPENSSL_SOURCE_DIR}/crypto/bio/bss_core.c
    ${OPENSSL_SOURCE_DIR}/crypto/bio/bss_dgram.c
    ${OPENSSL_SOURCE_DIR}/crypto/bio/bss_dgram_pair.c
    ${OPENSSL_SOURCE_DIR}/crypto/bio/bss_fd.c
    ${OPENSSL_SOURCE_DIR}/crypto/bio/bss_file.c
    ${OPENSSL_SOURCE_DIR}/crypto/bio/bss_log.c
    ${OPENSSL_SOURCE_DIR}/crypto/bio/bss_mem.c
    ${OPENSSL_SOURCE_DIR}/crypto/bio/bss_null.c
    ${OPENSSL_SOURCE_DIR}/crypto/bio/bss_sock.c
    ${OPENSSL_SOURCE_DIR}/crypto/bio/ossl_core_bio.c
    ${OPENSSL_SOURCE_DIR}/crypto/bn/bn_add.c
    ${OPENSSL_SOURCE_DIR}/crypto/bn/bn_blind.c
    ${OPENSSL_SOURCE_DIR}/crypto/bn/bn_const.c
    ${OPENSSL_SOURCE_DIR}/crypto/bn/bn_conv.c
    ${OPENSSL_SOURCE_DIR}/crypto/bn/bn_ctx.c
    ${OPENSSL_SOURCE_DIR}/crypto/bn/bn_depr.c
    ${OPENSSL_SOURCE_DIR}/crypto/bn/bn_dh.c
    ${OPENSSL_SOURCE_DIR}/crypto/bn/bn_div.c
    ${OPENSSL_SOURCE_DIR}/crypto/bn/bn_err.c
    ${OPENSSL_SOURCE_DIR}/crypto/bn/bn_exp.c
    ${OPENSSL_SOURCE_DIR}/crypto/bn/bn_exp2.c
    ${OPENSSL_SOURCE_DIR}/crypto/bn/bn_gcd.c
    ${OPENSSL_SOURCE_DIR}/crypto/bn/bn_gf2m.c
    ${OPENSSL_SOURCE_DIR}/crypto/bn/bn_intern.c
    ${OPENSSL_SOURCE_DIR}/crypto/bn/bn_kron.c
    ${OPENSSL_SOURCE_DIR}/crypto/bn/bn_lib.c
    ${OPENSSL_SOURCE_DIR}/crypto/bn/bn_mod.c
    ${OPENSSL_SOURCE_DIR}/crypto/bn/bn_mont.c
    ${OPENSSL_SOURCE_DIR}/crypto/bn/bn_mpi.c
    ${OPENSSL_SOURCE_DIR}/crypto/bn/bn_mul.c
    ${OPENSSL_SOURCE_DIR}/crypto/bn/bn_nist.c
    ${OPENSSL_SOURCE_DIR}/crypto/bn/bn_prime.c
    ${OPENSSL_SOURCE_DIR}/crypto/bn/bn_print.c
    ${OPENSSL_SOURCE_DIR}/crypto/bn/bn_rand.c
    ${OPENSSL_SOURCE_DIR}/crypto/bn/bn_recp.c
    ${OPENSSL_SOURCE_DIR}/crypto/bn/bn_rsa_fips186_4.c
    ${OPENSSL_SOURCE_DIR}/crypto/bn/bn_shift.c
    ${OPENSSL_SOURCE_DIR}/crypto/bn/bn_sqr.c
    ${OPENSSL_SOURCE_DIR}/crypto/bn/bn_sqrt.c
    ${OPENSSL_SOURCE_DIR}/crypto/bn/bn_word.c
    ${OPENSSL_SOURCE_DIR}/crypto/bn/bn_x931p.c
    ${OPENSSL_SOURCE_DIR}/crypto/bsearch.c
    ${OPENSSL_SOURCE_DIR}/crypto/buffer/buf_err.c
    ${OPENSSL_SOURCE_DIR}/crypto/buffer/buffer.c
    ${OPENSSL_SOURCE_DIR}/crypto/camellia/cmll_cfb.c
    ${OPENSSL_SOURCE_DIR}/crypto/camellia/cmll_ctr.c
    ${OPENSSL_SOURCE_DIR}/crypto/camellia/cmll_ecb.c
    ${OPENSSL_SOURCE_DIR}/crypto/camellia/cmll_misc.c
    ${OPENSSL_SOURCE_DIR}/crypto/camellia/cmll_ofb.c
    ${OPENSSL_SOURCE_DIR}/crypto/cast/c_cfb64.c
    ${OPENSSL_SOURCE_DIR}/crypto/cast/c_ecb.c
    ${OPENSSL_SOURCE_DIR}/crypto/cast/c_enc.c
    ${OPENSSL_SOURCE_DIR}/crypto/cast/c_ofb64.c
    ${OPENSSL_SOURCE_DIR}/crypto/cast/c_skey.c
    ${OPENSSL_SOURCE_DIR}/crypto/cmac/cmac.c
    ${OPENSSL_SOURCE_DIR}/crypto/cmp/cmp_asn.c
    ${OPENSSL_SOURCE_DIR}/crypto/cmp/cmp_client.c
    ${OPENSSL_SOURCE_DIR}/crypto/cmp/cmp_ctx.c
    ${OPENSSL_SOURCE_DIR}/crypto/cmp/cmp_err.c
    ${OPENSSL_SOURCE_DIR}/crypto/cmp/cmp_genm.c
    ${OPENSSL_SOURCE_DIR}/crypto/cmp/cmp_hdr.c
    ${OPENSSL_SOURCE_DIR}/crypto/cmp/cmp_http.c
    ${OPENSSL_SOURCE_DIR}/crypto/cmp/cmp_msg.c
    ${OPENSSL_SOURCE_DIR}/crypto/cmp/cmp_protect.c
    ${OPENSSL_SOURCE_DIR}/crypto/cmp/cmp_server.c
    ${OPENSSL_SOURCE_DIR}/crypto/cmp/cmp_status.c
    ${OPENSSL_SOURCE_DIR}/crypto/cmp/cmp_util.c
    ${OPENSSL_SOURCE_DIR}/crypto/cmp/cmp_vfy.c
    ${OPENSSL_SOURCE_DIR}/crypto/cms/cms_asn1.c
    ${OPENSSL_SOURCE_DIR}/crypto/cms/cms_att.c
    ${OPENSSL_SOURCE_DIR}/crypto/cms/cms_cd.c
    ${OPENSSL_SOURCE_DIR}/crypto/cms/cms_dd.c
    ${OPENSSL_SOURCE_DIR}/crypto/cms/cms_dh.c
    ${OPENSSL_SOURCE_DIR}/crypto/cms/cms_ec.c
    ${OPENSSL_SOURCE_DIR}/crypto/cms/cms_enc.c
    ${OPENSSL_SOURCE_DIR}/crypto/cms/cms_env.c
    ${OPENSSL_SOURCE_DIR}/crypto/cms/cms_err.c
    ${OPENSSL_SOURCE_DIR}/crypto/cms/cms_ess.c
    ${OPENSSL_SOURCE_DIR}/crypto/cms/cms_io.c
    ${OPENSSL_SOURCE_DIR}/crypto/cms/cms_kari.c
    ${OPENSSL_SOURCE_DIR}/crypto/cms/cms_lib.c
    ${OPENSSL_SOURCE_DIR}/crypto/cms/cms_pwri.c
    ${OPENSSL_SOURCE_DIR}/crypto/cms/cms_rsa.c
    ${OPENSSL_SOURCE_DIR}/crypto/cms/cms_sd.c
    ${OPENSSL_SOURCE_DIR}/crypto/cms/cms_smime.c
    ${OPENSSL_SOURCE_DIR}/crypto/comp/c_brotli.c
    ${OPENSSL_SOURCE_DIR}/crypto/comp/c_zlib.c
    ${OPENSSL_SOURCE_DIR}/crypto/comp/c_zstd.c
    ${OPENSSL_SOURCE_DIR}/crypto/comp/comp_err.c
    ${OPENSSL_SOURCE_DIR}/crypto/comp/comp_lib.c
    ${OPENSSL_SOURCE_DIR}/crypto/conf/conf_api.c
    ${OPENSSL_SOURCE_DIR}/crypto/conf/conf_def.c
    ${OPENSSL_SOURCE_DIR}/crypto/conf/conf_err.c
    ${OPENSSL_SOURCE_DIR}/crypto/conf/conf_lib.c
    ${OPENSSL_SOURCE_DIR}/crypto/conf/conf_mall.c
    ${OPENSSL_SOURCE_DIR}/crypto/conf/conf_mod.c
    ${OPENSSL_SOURCE_DIR}/crypto/conf/conf_sap.c
    ${OPENSSL_SOURCE_DIR}/crypto/conf/conf_ssl.c
    ${OPENSSL_SOURCE_DIR}/crypto/context.c
    ${OPENSSL_SOURCE_DIR}/crypto/core_algorithm.c
    ${OPENSSL_SOURCE_DIR}/crypto/core_fetch.c
    ${OPENSSL_SOURCE_DIR}/crypto/core_namemap.c
    ${OPENSSL_SOURCE_DIR}/crypto/cpt_err.c
    ${OPENSSL_SOURCE_DIR}/crypto/cpuid.c
    ${OPENSSL_SOURCE_DIR}/crypto/crmf/crmf_asn.c
    ${OPENSSL_SOURCE_DIR}/crypto/crmf/crmf_err.c
    ${OPENSSL_SOURCE_DIR}/crypto/crmf/crmf_lib.c
    ${OPENSSL_SOURCE_DIR}/crypto/crmf/crmf_pbm.c
    ${OPENSSL_SOURCE_DIR}/crypto/cryptlib.c
    ${OPENSSL_SOURCE_DIR}/crypto/ct/ct_b64.c
    ${OPENSSL_SOURCE_DIR}/crypto/ct/ct_err.c
    ${OPENSSL_SOURCE_DIR}/crypto/ct/ct_log.c
    ${OPENSSL_SOURCE_DIR}/crypto/ct/ct_oct.c
    ${OPENSSL_SOURCE_DIR}/crypto/ct/ct_policy.c
    ${OPENSSL_SOURCE_DIR}/crypto/ct/ct_prn.c
    ${OPENSSL_SOURCE_DIR}/crypto/ct/ct_sct.c
    ${OPENSSL_SOURCE_DIR}/crypto/ct/ct_sct_ctx.c
    ${OPENSSL_SOURCE_DIR}/crypto/ct/ct_vfy.c
    ${OPENSSL_SOURCE_DIR}/crypto/ct/ct_x509v3.c
    ${OPENSSL_SOURCE_DIR}/crypto/ctype.c
    ${OPENSSL_SOURCE_DIR}/crypto/cversion.c
    ${OPENSSL_SOURCE_DIR}/crypto/der_writer.c
    ${OPENSSL_SOURCE_DIR}/crypto/des/cbc_cksm.c
    ${OPENSSL_SOURCE_DIR}/crypto/des/cbc_enc.c
    ${OPENSSL_SOURCE_DIR}/crypto/des/cfb64ede.c
    ${OPENSSL_SOURCE_DIR}/crypto/des/cfb64enc.c
    ${OPENSSL_SOURCE_DIR}/crypto/des/cfb_enc.c
    ${OPENSSL_SOURCE_DIR}/crypto/des/des_enc.c
    ${OPENSSL_SOURCE_DIR}/crypto/des/ecb3_enc.c
    ${OPENSSL_SOURCE_DIR}/crypto/des/ecb_enc.c
    ${OPENSSL_SOURCE_DIR}/crypto/des/fcrypt.c
    ${OPENSSL_SOURCE_DIR}/crypto/des/fcrypt_b.c
    ${OPENSSL_SOURCE_DIR}/crypto/des/ofb64ede.c
    ${OPENSSL_SOURCE_DIR}/crypto/des/ofb64enc.c
    ${OPENSSL_SOURCE_DIR}/crypto/des/ofb_enc.c
    ${OPENSSL_SOURCE_DIR}/crypto/des/pcbc_enc.c
    ${OPENSSL_SOURCE_DIR}/crypto/des/qud_cksm.c
    ${OPENSSL_SOURCE_DIR}/crypto/des/rand_key.c
    ${OPENSSL_SOURCE_DIR}/crypto/des/set_key.c
    ${OPENSSL_SOURCE_DIR}/crypto/des/str2key.c
    ${OPENSSL_SOURCE_DIR}/crypto/des/xcbc_enc.c
    ${OPENSSL_SOURCE_DIR}/crypto/deterministic_nonce.c
    ${OPENSSL_SOURCE_DIR}/crypto/dh/dh_ameth.c
    ${OPENSSL_SOURCE_DIR}/crypto/dh/dh_asn1.c
    ${OPENSSL_SOURCE_DIR}/crypto/dh/dh_backend.c
    ${OPENSSL_SOURCE_DIR}/crypto/dh/dh_check.c
    ${OPENSSL_SOURCE_DIR}/crypto/dh/dh_depr.c
    ${OPENSSL_SOURCE_DIR}/crypto/dh/dh_err.c
    ${OPENSSL_SOURCE_DIR}/crypto/dh/dh_gen.c
    ${OPENSSL_SOURCE_DIR}/crypto/dh/dh_group_params.c
    ${OPENSSL_SOURCE_DIR}/crypto/dh/dh_kdf.c
    ${OPENSSL_SOURCE_DIR}/crypto/dh/dh_key.c
    ${OPENSSL_SOURCE_DIR}/crypto/dh/dh_lib.c
    ${OPENSSL_SOURCE_DIR}/crypto/dh/dh_meth.c
    ${OPENSSL_SOURCE_DIR}/crypto/dh/dh_pmeth.c
    ${OPENSSL_SOURCE_DIR}/crypto/dh/dh_prn.c
    ${OPENSSL_SOURCE_DIR}/crypto/dh/dh_rfc5114.c
    ${OPENSSL_SOURCE_DIR}/crypto/dsa/dsa_ameth.c
    ${OPENSSL_SOURCE_DIR}/crypto/dsa/dsa_asn1.c
    ${OPENSSL_SOURCE_DIR}/crypto/dsa/dsa_backend.c
    ${OPENSSL_SOURCE_DIR}/crypto/dsa/dsa_check.c
    ${OPENSSL_SOURCE_DIR}/crypto/dsa/dsa_depr.c
    ${OPENSSL_SOURCE_DIR}/crypto/dsa/dsa_err.c
    ${OPENSSL_SOURCE_DIR}/crypto/dsa/dsa_gen.c
    ${OPENSSL_SOURCE_DIR}/crypto/dsa/dsa_key.c
    ${OPENSSL_SOURCE_DIR}/crypto/dsa/dsa_lib.c
    ${OPENSSL_SOURCE_DIR}/crypto/dsa/dsa_meth.c
    ${OPENSSL_SOURCE_DIR}/crypto/dsa/dsa_ossl.c
    ${OPENSSL_SOURCE_DIR}/crypto/dsa/dsa_pmeth.c
    ${OPENSSL_SOURCE_DIR}/crypto/dsa/dsa_prn.c
    ${OPENSSL_SOURCE_DIR}/crypto/dsa/dsa_sign.c
    ${OPENSSL_SOURCE_DIR}/crypto/dsa/dsa_vrf.c
    ${OPENSSL_SOURCE_DIR}/crypto/dso/dso_dl.c
    ${OPENSSL_SOURCE_DIR}/crypto/dso/dso_dlfcn.c
    ${OPENSSL_SOURCE_DIR}/crypto/dso/dso_err.c
    ${OPENSSL_SOURCE_DIR}/crypto/dso/dso_lib.c
    ${OPENSSL_SOURCE_DIR}/crypto/dso/dso_openssl.c
    ${OPENSSL_SOURCE_DIR}/crypto/dso/dso_vms.c
    ${OPENSSL_SOURCE_DIR}/crypto/dso/dso_win32.c
    ${OPENSSL_SOURCE_DIR}/crypto/ebcdic.c
    ${OPENSSL_SOURCE_DIR}/crypto/ec/curve25519.c
    ${OPENSSL_SOURCE_DIR}/crypto/ec/curve448/arch_32/f_impl32.c
    ${OPENSSL_SOURCE_DIR}/crypto/ec/curve448/arch_64/f_impl64.c
    ${OPENSSL_SOURCE_DIR}/crypto/ec/curve448/curve448.c
    ${OPENSSL_SOURCE_DIR}/crypto/ec/curve448/curve448_tables.c
    ${OPENSSL_SOURCE_DIR}/crypto/ec/curve448/eddsa.c
    ${OPENSSL_SOURCE_DIR}/crypto/ec/curve448/f_generic.c
    ${OPENSSL_SOURCE_DIR}/crypto/ec/curve448/scalar.c
    ${OPENSSL_SOURCE_DIR}/crypto/ec/ec2_oct.c
    ${OPENSSL_SOURCE_DIR}/crypto/ec/ec2_smpl.c
    ${OPENSSL_SOURCE_DIR}/crypto/ec/ec_ameth.c
    ${OPENSSL_SOURCE_DIR}/crypto/ec/ec_asn1.c
    ${OPENSSL_SOURCE_DIR}/crypto/ec/ec_backend.c
    ${OPENSSL_SOURCE_DIR}/crypto/ec/ec_check.c
    ${OPENSSL_SOURCE_DIR}/crypto/ec/ec_curve.c
    ${OPENSSL_SOURCE_DIR}/crypto/ec/ec_cvt.c
    ${OPENSSL_SOURCE_DIR}/crypto/ec/ec_deprecated.c
    ${OPENSSL_SOURCE_DIR}/crypto/ec/ec_err.c
    ${OPENSSL_SOURCE_DIR}/crypto/ec/ec_key.c
    ${OPENSSL_SOURCE_DIR}/crypto/ec/ec_kmeth.c
    ${OPENSSL_SOURCE_DIR}/crypto/ec/ec_lib.c
    ${OPENSSL_SOURCE_DIR}/crypto/ec/ec_mult.c
    ${OPENSSL_SOURCE_DIR}/crypto/ec/ec_oct.c
    ${OPENSSL_SOURCE_DIR}/crypto/ec/ec_pmeth.c
    ${OPENSSL_SOURCE_DIR}/crypto/ec/ec_print.c
    ${OPENSSL_SOURCE_DIR}/crypto/ec/ecdh_kdf.c
    ${OPENSSL_SOURCE_DIR}/crypto/ec/ecdh_ossl.c
    ${OPENSSL_SOURCE_DIR}/crypto/ec/ecdsa_ossl.c
    ${OPENSSL_SOURCE_DIR}/crypto/ec/ecdsa_sign.c
    ${OPENSSL_SOURCE_DIR}/crypto/ec/ecdsa_vrf.c
    ${OPENSSL_SOURCE_DIR}/crypto/ec/eck_prn.c
    ${OPENSSL_SOURCE_DIR}/crypto/ec/ecp_mont.c
    ${OPENSSL_SOURCE_DIR}/crypto/ec/ecp_nist.c
    ${OPENSSL_SOURCE_DIR}/crypto/ec/ecp_oct.c
    ${OPENSSL_SOURCE_DIR}/crypto/ec/ecp_smpl.c
    ${OPENSSL_SOURCE_DIR}/crypto/ec/ecx_backend.c
    ${OPENSSL_SOURCE_DIR}/crypto/ec/ecx_key.c
    ${OPENSSL_SOURCE_DIR}/crypto/ec/ecx_meth.c
    ${OPENSSL_SOURCE_DIR}/crypto/encode_decode/decoder_err.c
    ${OPENSSL_SOURCE_DIR}/crypto/encode_decode/decoder_lib.c
    ${OPENSSL_SOURCE_DIR}/crypto/encode_decode/decoder_meth.c
    ${OPENSSL_SOURCE_DIR}/crypto/encode_decode/decoder_pkey.c
    ${OPENSSL_SOURCE_DIR}/crypto/encode_decode/encoder_err.c
    ${OPENSSL_SOURCE_DIR}/crypto/encode_decode/encoder_lib.c
    ${OPENSSL_SOURCE_DIR}/crypto/encode_decode/encoder_meth.c
    ${OPENSSL_SOURCE_DIR}/crypto/encode_decode/encoder_pkey.c
    ${OPENSSL_SOURCE_DIR}/crypto/err/err.c
    ${OPENSSL_SOURCE_DIR}/crypto/err/err_all.c
    ${OPENSSL_SOURCE_DIR}/crypto/err/err_all_legacy.c
    ${OPENSSL_SOURCE_DIR}/crypto/err/err_blocks.c
    ${OPENSSL_SOURCE_DIR}/crypto/err/err_mark.c
    ${OPENSSL_SOURCE_DIR}/crypto/err/err_prn.c
    ${OPENSSL_SOURCE_DIR}/crypto/err/err_save.c
    ${OPENSSL_SOURCE_DIR}/crypto/ess/ess_asn1.c
    ${OPENSSL_SOURCE_DIR}/crypto/ess/ess_err.c
    ${OPENSSL_SOURCE_DIR}/crypto/ess/ess_lib.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/asymcipher.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/bio_b64.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/bio_enc.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/bio_md.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/bio_ok.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/c_allc.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/c_alld.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/cmeth_lib.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/pmeth_lib.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/pmeth_gn.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/pmeth_check.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/pbe_scrypt.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/signature.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/ctrl_params_translate.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/dh_ctrl.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/dh_support.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/digest.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/dsa_ctrl.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/e_aes.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/e_aes_cbc_hmac_sha1.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/e_aes_cbc_hmac_sha256.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/e_aria.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/e_bf.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/e_camellia.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/e_cast.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/e_chacha20_poly1305.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/e_des.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/e_des3.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/e_idea.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/e_null.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/e_old.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/e_rc2.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/e_rc4.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/e_rc4_hmac_md5.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/e_rc5.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/e_seed.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/e_sm4.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/e_xcbc_d.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/ec_ctrl.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/ec_support.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/encode.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/evp_cnf.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/evp_enc.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/evp_err.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/evp_fetch.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/evp_key.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/evp_lib.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/evp_pbe.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/evp_pkey.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/evp_rand.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/evp_utils.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/exchange.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/kdf_lib.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/kdf_meth.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/kem.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/keymgmt_lib.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/keymgmt_meth.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/legacy_blake2.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/legacy_md4.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/legacy_md5.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/legacy_md5_sha1.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/legacy_mdc2.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/legacy_ripemd.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/legacy_sha.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/legacy_wp.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/m_null.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/m_sigver.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/mac_lib.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/mac_meth.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/names.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/p5_crpt.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/p5_crpt2.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/p_legacy.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/p_lib.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/p_open.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/p_seal.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/p_sign.c
    ${OPENSSL_SOURCE_DIR}/crypto/evp/p_verify.c
    ${OPENSSL_SOURCE_DIR}/crypto/ex_data.c
    ${OPENSSL_SOURCE_DIR}/crypto/ffc/ffc_backend.c
    ${OPENSSL_SOURCE_DIR}/crypto/ffc/ffc_dh.c
    ${OPENSSL_SOURCE_DIR}/crypto/ffc/ffc_key_generate.c
    ${OPENSSL_SOURCE_DIR}/crypto/ffc/ffc_key_validate.c
    ${OPENSSL_SOURCE_DIR}/crypto/ffc/ffc_params.c
    ${OPENSSL_SOURCE_DIR}/crypto/ffc/ffc_params_generate.c
    ${OPENSSL_SOURCE_DIR}/crypto/ffc/ffc_params_validate.c
    ${OPENSSL_SOURCE_DIR}/crypto/getenv.c
    ${OPENSSL_SOURCE_DIR}/crypto/hmac/hmac.c
    ${OPENSSL_SOURCE_DIR}/crypto/hpke/hpke.c
    ${OPENSSL_SOURCE_DIR}/crypto/hpke/hpke_util.c
    ${OPENSSL_SOURCE_DIR}/crypto/http/http_client.c
    ${OPENSSL_SOURCE_DIR}/crypto/http/http_err.c
    ${OPENSSL_SOURCE_DIR}/crypto/http/http_lib.c
    ${OPENSSL_SOURCE_DIR}/crypto/idea/i_cbc.c
    ${OPENSSL_SOURCE_DIR}/crypto/idea/i_cfb64.c
    ${OPENSSL_SOURCE_DIR}/crypto/idea/i_ecb.c
    ${OPENSSL_SOURCE_DIR}/crypto/idea/i_ofb64.c
    ${OPENSSL_SOURCE_DIR}/crypto/idea/i_skey.c
    ${OPENSSL_SOURCE_DIR}/crypto/info.c
    ${OPENSSL_SOURCE_DIR}/crypto/init.c
    ${OPENSSL_SOURCE_DIR}/crypto/initthread.c
    ${OPENSSL_SOURCE_DIR}/crypto/kdf/kdf_err.c
    ${OPENSSL_SOURCE_DIR}/crypto/lhash/lh_stats.c
    ${OPENSSL_SOURCE_DIR}/crypto/lhash/lhash.c
    ${OPENSSL_SOURCE_DIR}/crypto/md4/md4_dgst.c
    ${OPENSSL_SOURCE_DIR}/crypto/md4/md4_one.c
    ${OPENSSL_SOURCE_DIR}/crypto/md5/md5_dgst.c
    ${OPENSSL_SOURCE_DIR}/crypto/md5/md5_one.c
    ${OPENSSL_SOURCE_DIR}/crypto/md5/md5_sha1.c
    ${OPENSSL_SOURCE_DIR}/crypto/mdc2/mdc2_one.c
    ${OPENSSL_SOURCE_DIR}/crypto/mdc2/mdc2dgst.c
    ${OPENSSL_SOURCE_DIR}/crypto/mem.c
    ${OPENSSL_SOURCE_DIR}/crypto/mem_sec.c
    ${OPENSSL_SOURCE_DIR}/crypto/modes/cbc128.c
    ${OPENSSL_SOURCE_DIR}/crypto/modes/ccm128.c
    ${OPENSSL_SOURCE_DIR}/crypto/modes/cfb128.c
    ${OPENSSL_SOURCE_DIR}/crypto/modes/ctr128.c
    ${OPENSSL_SOURCE_DIR}/crypto/modes/cts128.c
    ${OPENSSL_SOURCE_DIR}/crypto/modes/gcm128.c
    ${OPENSSL_SOURCE_DIR}/crypto/modes/ocb128.c
    ${OPENSSL_SOURCE_DIR}/crypto/modes/ofb128.c
    ${OPENSSL_SOURCE_DIR}/crypto/modes/siv128.c
    ${OPENSSL_SOURCE_DIR}/crypto/modes/wrap128.c
    ${OPENSSL_SOURCE_DIR}/crypto/modes/xts128.c
    ${OPENSSL_SOURCE_DIR}/crypto/modes/xts128gb.c
    ${OPENSSL_SOURCE_DIR}/crypto/o_dir.c
    ${OPENSSL_SOURCE_DIR}/crypto/o_fopen.c
    ${OPENSSL_SOURCE_DIR}/crypto/o_init.c
    ${OPENSSL_SOURCE_DIR}/crypto/o_str.c
    ${OPENSSL_SOURCE_DIR}/crypto/o_time.c
    ${OPENSSL_SOURCE_DIR}/crypto/objects/o_names.c
    ${OPENSSL_SOURCE_DIR}/crypto/objects/obj_dat.c
    ${OPENSSL_SOURCE_DIR}/crypto/objects/obj_err.c
    ${OPENSSL_SOURCE_DIR}/crypto/objects/obj_lib.c
    ${OPENSSL_SOURCE_DIR}/crypto/objects/obj_xref.c
    ${OPENSSL_SOURCE_DIR}/crypto/ocsp/ocsp_asn.c
    ${OPENSSL_SOURCE_DIR}/crypto/ocsp/ocsp_cl.c
    ${OPENSSL_SOURCE_DIR}/crypto/ocsp/ocsp_err.c
    ${OPENSSL_SOURCE_DIR}/crypto/ocsp/ocsp_ext.c
    ${OPENSSL_SOURCE_DIR}/crypto/ocsp/ocsp_http.c
    ${OPENSSL_SOURCE_DIR}/crypto/ocsp/ocsp_lib.c
    ${OPENSSL_SOURCE_DIR}/crypto/ocsp/ocsp_prn.c
    ${OPENSSL_SOURCE_DIR}/crypto/ocsp/ocsp_srv.c
    ${OPENSSL_SOURCE_DIR}/crypto/ocsp/ocsp_vfy.c
    ${OPENSSL_SOURCE_DIR}/crypto/ocsp/v3_ocsp.c
    ${OPENSSL_SOURCE_DIR}/crypto/packet.c
    ${OPENSSL_SOURCE_DIR}/crypto/param_build.c
    ${OPENSSL_SOURCE_DIR}/crypto/param_build_set.c
    ${OPENSSL_SOURCE_DIR}/crypto/params.c
    ${OPENSSL_SOURCE_DIR}/crypto/params_dup.c
    ${OPENSSL_SOURCE_DIR}/crypto/params_from_text.c
    ${OPENSSL_SOURCE_DIR}/crypto/passphrase.c
    ${OPENSSL_SOURCE_DIR}/crypto/pem/pem_all.c
    ${OPENSSL_SOURCE_DIR}/crypto/pem/pem_err.c
    ${OPENSSL_SOURCE_DIR}/crypto/pem/pem_info.c
    ${OPENSSL_SOURCE_DIR}/crypto/pem/pem_lib.c
    ${OPENSSL_SOURCE_DIR}/crypto/pem/pem_oth.c
    ${OPENSSL_SOURCE_DIR}/crypto/pem/pem_pk8.c
    ${OPENSSL_SOURCE_DIR}/crypto/pem/pem_pkey.c
    ${OPENSSL_SOURCE_DIR}/crypto/pem/pem_sign.c
    ${OPENSSL_SOURCE_DIR}/crypto/pem/pem_x509.c
    ${OPENSSL_SOURCE_DIR}/crypto/pem/pem_xaux.c
    ${OPENSSL_SOURCE_DIR}/crypto/pem/pvkfmt.c
    ${OPENSSL_SOURCE_DIR}/crypto/pkcs12/p12_add.c
    ${OPENSSL_SOURCE_DIR}/crypto/pkcs12/p12_asn.c
    ${OPENSSL_SOURCE_DIR}/crypto/pkcs12/p12_attr.c
    ${OPENSSL_SOURCE_DIR}/crypto/pkcs12/p12_crpt.c
    ${OPENSSL_SOURCE_DIR}/crypto/pkcs12/p12_crt.c
    ${OPENSSL_SOURCE_DIR}/crypto/pkcs12/p12_decr.c
    ${OPENSSL_SOURCE_DIR}/crypto/pkcs12/p12_init.c
    ${OPENSSL_SOURCE_DIR}/crypto/pkcs12/p12_key.c
    ${OPENSSL_SOURCE_DIR}/crypto/pkcs12/p12_kiss.c
    ${OPENSSL_SOURCE_DIR}/crypto/pkcs12/p12_mutl.c
    ${OPENSSL_SOURCE_DIR}/crypto/pkcs12/p12_npas.c
    ${OPENSSL_SOURCE_DIR}/crypto/pkcs12/p12_p8d.c
    ${OPENSSL_SOURCE_DIR}/crypto/pkcs12/p12_p8e.c
    ${OPENSSL_SOURCE_DIR}/crypto/pkcs12/p12_sbag.c
    ${OPENSSL_SOURCE_DIR}/crypto/pkcs12/p12_utl.c
    ${OPENSSL_SOURCE_DIR}/crypto/pkcs12/pk12err.c
    ${OPENSSL_SOURCE_DIR}/crypto/pkcs7/bio_pk7.c
    ${OPENSSL_SOURCE_DIR}/crypto/pkcs7/pk7_asn1.c
    ${OPENSSL_SOURCE_DIR}/crypto/pkcs7/pk7_attr.c
    ${OPENSSL_SOURCE_DIR}/crypto/pkcs7/pk7_doit.c
    ${OPENSSL_SOURCE_DIR}/crypto/pkcs7/pk7_lib.c
    ${OPENSSL_SOURCE_DIR}/crypto/pkcs7/pk7_mime.c
    ${OPENSSL_SOURCE_DIR}/crypto/pkcs7/pk7_smime.c
    ${OPENSSL_SOURCE_DIR}/crypto/pkcs7/pkcs7err.c
    ${OPENSSL_SOURCE_DIR}/crypto/poly1305/poly1305.c
    ${OPENSSL_SOURCE_DIR}/crypto/property/defn_cache.c
    ${OPENSSL_SOURCE_DIR}/crypto/property/property.c
    ${OPENSSL_SOURCE_DIR}/crypto/property/property_err.c
    ${OPENSSL_SOURCE_DIR}/crypto/property/property_parse.c
    ${OPENSSL_SOURCE_DIR}/crypto/property/property_query.c
    ${OPENSSL_SOURCE_DIR}/crypto/property/property_string.c
    ${OPENSSL_SOURCE_DIR}/crypto/provider.c
    ${OPENSSL_SOURCE_DIR}/crypto/provider_child.c
    ${OPENSSL_SOURCE_DIR}/crypto/provider_conf.c
    ${OPENSSL_SOURCE_DIR}/crypto/provider_core.c
    ${OPENSSL_SOURCE_DIR}/crypto/provider_predefined.c
    ${OPENSSL_SOURCE_DIR}/crypto/punycode.c
    ${OPENSSL_SOURCE_DIR}/crypto/quic_vlint.c
    ${OPENSSL_SOURCE_DIR}/crypto/rand/rand_deprecated.c
    ${OPENSSL_SOURCE_DIR}/crypto/rand/rand_err.c
    ${OPENSSL_SOURCE_DIR}/crypto/rand/rand_lib.c
    ${OPENSSL_SOURCE_DIR}/crypto/rand/rand_pool.c
    ${OPENSSL_SOURCE_DIR}/crypto/rand/rand_uniform.c
    ${OPENSSL_SOURCE_DIR}/crypto/rand/randfile.c
    ${OPENSSL_SOURCE_DIR}/crypto/rand/prov_seed.c
    ${OPENSSL_SOURCE_DIR}/crypto/rc2/rc2_cbc.c
    ${OPENSSL_SOURCE_DIR}/crypto/rc2/rc2_ecb.c
    ${OPENSSL_SOURCE_DIR}/crypto/rc2/rc2_skey.c
    ${OPENSSL_SOURCE_DIR}/crypto/rc2/rc2cfb64.c
    ${OPENSSL_SOURCE_DIR}/crypto/rc2/rc2ofb64.c
    ${OPENSSL_SOURCE_DIR}/crypto/ripemd/rmd_dgst.c
    ${OPENSSL_SOURCE_DIR}/crypto/ripemd/rmd_one.c
    ${OPENSSL_SOURCE_DIR}/crypto/rsa/rsa_ameth.c
    ${OPENSSL_SOURCE_DIR}/crypto/rsa/rsa_asn1.c
    ${OPENSSL_SOURCE_DIR}/crypto/rsa/rsa_backend.c
    ${OPENSSL_SOURCE_DIR}/crypto/rsa/rsa_chk.c
    ${OPENSSL_SOURCE_DIR}/crypto/rsa/rsa_crpt.c
    ${OPENSSL_SOURCE_DIR}/crypto/rsa/rsa_depr.c
    ${OPENSSL_SOURCE_DIR}/crypto/rsa/rsa_err.c
    ${OPENSSL_SOURCE_DIR}/crypto/rsa/rsa_gen.c
    ${OPENSSL_SOURCE_DIR}/crypto/rsa/rsa_lib.c
    ${OPENSSL_SOURCE_DIR}/crypto/rsa/rsa_meth.c
    ${OPENSSL_SOURCE_DIR}/crypto/rsa/rsa_mp.c
    ${OPENSSL_SOURCE_DIR}/crypto/rsa/rsa_mp_names.c
    ${OPENSSL_SOURCE_DIR}/crypto/rsa/rsa_none.c
    ${OPENSSL_SOURCE_DIR}/crypto/rsa/rsa_oaep.c
    ${OPENSSL_SOURCE_DIR}/crypto/rsa/rsa_ossl.c
    ${OPENSSL_SOURCE_DIR}/crypto/rsa/rsa_pk1.c
    ${OPENSSL_SOURCE_DIR}/crypto/rsa/rsa_pmeth.c
    ${OPENSSL_SOURCE_DIR}/crypto/rsa/rsa_prn.c
    ${OPENSSL_SOURCE_DIR}/crypto/rsa/rsa_pss.c
    ${OPENSSL_SOURCE_DIR}/crypto/rsa/rsa_saos.c
    ${OPENSSL_SOURCE_DIR}/crypto/rsa/rsa_schemes.c
    ${OPENSSL_SOURCE_DIR}/crypto/rsa/rsa_sign.c
    ${OPENSSL_SOURCE_DIR}/crypto/rsa/rsa_sp800_56b_check.c
    ${OPENSSL_SOURCE_DIR}/crypto/rsa/rsa_sp800_56b_gen.c
    ${OPENSSL_SOURCE_DIR}/crypto/rsa/rsa_x931.c
    ${OPENSSL_SOURCE_DIR}/crypto/seed/seed.c
    ${OPENSSL_SOURCE_DIR}/crypto/seed/seed_cbc.c
    ${OPENSSL_SOURCE_DIR}/crypto/seed/seed_cfb.c
    ${OPENSSL_SOURCE_DIR}/crypto/seed/seed_ecb.c
    ${OPENSSL_SOURCE_DIR}/crypto/seed/seed_ofb.c
    ${OPENSSL_SOURCE_DIR}/crypto/self_test_core.c
    ${OPENSSL_SOURCE_DIR}/crypto/sha/sha1_one.c
    ${OPENSSL_SOURCE_DIR}/crypto/sha/sha1dgst.c
    ${OPENSSL_SOURCE_DIR}/crypto/sha/sha256.c
    ${OPENSSL_SOURCE_DIR}/crypto/sha/sha3.c
    ${OPENSSL_SOURCE_DIR}/crypto/sha/sha512.c
    ${OPENSSL_SOURCE_DIR}/crypto/siphash/siphash.c
    ${OPENSSL_SOURCE_DIR}/crypto/sleep.c
    ${OPENSSL_SOURCE_DIR}/crypto/sm2/sm2_crypt.c
    ${OPENSSL_SOURCE_DIR}/crypto/sm2/sm2_err.c
    ${OPENSSL_SOURCE_DIR}/crypto/sm2/sm2_key.c
    ${OPENSSL_SOURCE_DIR}/crypto/sm2/sm2_sign.c
    ${OPENSSL_SOURCE_DIR}/crypto/sm3/legacy_sm3.c
    ${OPENSSL_SOURCE_DIR}/crypto/sm3/sm3.c
    ${OPENSSL_SOURCE_DIR}/crypto/sm4/sm4.c
    ${OPENSSL_SOURCE_DIR}/crypto/sparse_array.c
    ${OPENSSL_SOURCE_DIR}/crypto/stack/stack.c
    ${OPENSSL_SOURCE_DIR}/crypto/store/store_err.c
    ${OPENSSL_SOURCE_DIR}/crypto/store/store_lib.c
    ${OPENSSL_SOURCE_DIR}/crypto/store/store_meth.c
    ${OPENSSL_SOURCE_DIR}/crypto/store/store_result.c
    ${OPENSSL_SOURCE_DIR}/crypto/store/store_strings.c
    ${OPENSSL_SOURCE_DIR}/crypto/thread/api.c
    ${OPENSSL_SOURCE_DIR}/crypto/thread/arch.c
    ${OPENSSL_SOURCE_DIR}/crypto/thread/arch/thread_none.c
    ${OPENSSL_SOURCE_DIR}/crypto/thread/arch/thread_posix.c
    ${OPENSSL_SOURCE_DIR}/crypto/thread/arch/thread_win.c
    ${OPENSSL_SOURCE_DIR}/crypto/thread/internal.c
    ${OPENSSL_SOURCE_DIR}/crypto/threads_lib.c
    ${OPENSSL_SOURCE_DIR}/crypto/threads_none.c
    ${OPENSSL_SOURCE_DIR}/crypto/threads_pthread.c
    ${OPENSSL_SOURCE_DIR}/crypto/threads_win.c
    ${OPENSSL_SOURCE_DIR}/crypto/time.c
    ${OPENSSL_SOURCE_DIR}/crypto/trace.c
    ${OPENSSL_SOURCE_DIR}/crypto/ts/ts_asn1.c
    ${OPENSSL_SOURCE_DIR}/crypto/ts/ts_conf.c
    ${OPENSSL_SOURCE_DIR}/crypto/ts/ts_err.c
    ${OPENSSL_SOURCE_DIR}/crypto/ts/ts_lib.c
    ${OPENSSL_SOURCE_DIR}/crypto/ts/ts_req_print.c
    ${OPENSSL_SOURCE_DIR}/crypto/ts/ts_req_utils.c
    ${OPENSSL_SOURCE_DIR}/crypto/ts/ts_rsp_print.c
    ${OPENSSL_SOURCE_DIR}/crypto/ts/ts_rsp_sign.c
    ${OPENSSL_SOURCE_DIR}/crypto/ts/ts_rsp_utils.c
    ${OPENSSL_SOURCE_DIR}/crypto/ts/ts_rsp_verify.c
    ${OPENSSL_SOURCE_DIR}/crypto/ts/ts_verify_ctx.c
    ${OPENSSL_SOURCE_DIR}/crypto/txt_db/txt_db.c
    ${OPENSSL_SOURCE_DIR}/crypto/ui/ui_err.c
    ${OPENSSL_SOURCE_DIR}/crypto/ui/ui_lib.c
    ${OPENSSL_SOURCE_DIR}/crypto/ui/ui_null.c
    ${OPENSSL_SOURCE_DIR}/crypto/ui/ui_openssl.c
    ${OPENSSL_SOURCE_DIR}/crypto/ui/ui_util.c
    ${OPENSSL_SOURCE_DIR}/crypto/uid.c
    ${OPENSSL_SOURCE_DIR}/crypto/whrlpool/wp_dgst.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/by_dir.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/by_file.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/by_store.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/pcy_cache.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/pcy_data.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/pcy_lib.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/pcy_map.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/pcy_node.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/pcy_tree.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/t_crl.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/t_req.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/t_x509.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/v3_addr.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/v3_admis.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/v3_akeya.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/v3_akid.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/v3_asid.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/v3_bcons.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/v3_bitst.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/v3_conf.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/v3_cpols.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/v3_crld.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/v3_enum.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/v3_extku.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/v3_genn.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/v3_group_ac.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/v3_ia5.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/v3_ind_iss.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/v3_info.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/v3_int.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/v3_ist.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/v3_lib.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/v3_ncons.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/v3_no_ass.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/v3_no_rev_avail.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/v3_pci.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/v3_pcia.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/v3_pcons.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/v3_pku.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/v3_pmaps.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/v3_prn.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/v3_purp.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/v3_san.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/v3_single_use.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/v3_skid.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/v3_soa_id.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/v3_sxnet.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/v3_tlsf.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/v3_utf8.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/v3_utl.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/v3err.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/x509_att.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/x509_cmp.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/x509_d2.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/x509_def.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/x509_err.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/x509_ext.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/x509_lu.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/x509_meth.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/x509_obj.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/x509_r2x.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/x509_req.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/x509_set.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/x509_trust.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/x509_txt.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/x509_v3.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/x509_vfy.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/x509_vpm.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/x509cset.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/x509name.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/x509rset.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/x509spki.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/x_all.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/x_attrib.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/x_crl.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/x_exten.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/x_name.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/x_pubkey.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/x_req.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/x_x509.c
    ${OPENSSL_SOURCE_DIR}/crypto/x509/x_x509a.c
    ${OPENSSL_SOURCE_DIR}/providers/baseprov.c
    ${OPENSSL_SOURCE_DIR}/providers/common/bio_prov.c
    ${OPENSSL_SOURCE_DIR}/providers/common/capabilities.c
    ${OPENSSL_SOURCE_DIR}/providers/common/der/der_dsa_key.c
    ${OPENSSL_SOURCE_DIR}/providers/common/der/der_dsa_sig.c
    ${OPENSSL_SOURCE_DIR}/providers/common/der/der_ec_key.c
    ${OPENSSL_SOURCE_DIR}/providers/common/der/der_ec_sig.c
    ${OPENSSL_SOURCE_DIR}/providers/common/der/der_ecx_key.c
    ${OPENSSL_SOURCE_DIR}/providers/common/der/der_rsa_key.c
    ${OPENSSL_SOURCE_DIR}/providers/common/der/der_rsa_sig.c
    ${OPENSSL_SOURCE_DIR}/providers/common/der/der_sm2_key.c
    ${OPENSSL_SOURCE_DIR}/providers/common/der/der_sm2_sig.c
    ${OPENSSL_SOURCE_DIR}/providers/common/digest_to_nid.c
    ${OPENSSL_SOURCE_DIR}/providers/common/provider_ctx.c
    ${OPENSSL_SOURCE_DIR}/providers/common/provider_err.c
    ${OPENSSL_SOURCE_DIR}/providers/common/provider_seeding.c
    ${OPENSSL_SOURCE_DIR}/providers/common/provider_util.c
    ${OPENSSL_SOURCE_DIR}/providers/common/securitycheck.c
    ${OPENSSL_SOURCE_DIR}/providers/common/securitycheck_default.c
    ${OPENSSL_SOURCE_DIR}/providers/defltprov.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/asymciphers/rsa_enc.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/asymciphers/sm2_enc.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_aes.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_aes_cbc_hmac_sha.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_aes_cbc_hmac_sha1_hw.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_aes_cbc_hmac_sha256_hw.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_aes_ccm.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_aes_ccm_hw.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_aes_gcm.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_aes_gcm_hw.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_aes_gcm_siv.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_aes_gcm_siv_hw.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_aes_gcm_siv_polyval.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_aes_hw.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_aes_ocb.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_aes_ocb_hw.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_aes_siv.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_aes_siv_hw.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_aes_wrp.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_aes_xts.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_aes_xts_fips.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_aes_xts_hw.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_aria.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_aria_ccm.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_aria_ccm_hw.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_aria_gcm.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_aria_gcm_hw.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_aria_hw.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_blowfish.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_blowfish_hw.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_camellia.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_camellia_hw.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_cast5.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_cast5_hw.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_chacha20.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_chacha20_hw.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_chacha20_poly1305.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_chacha20_poly1305_hw.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_cts.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_des.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_des_hw.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_desx.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_desx_hw.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_idea.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_idea_hw.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_null.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_rc2.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_rc2_hw.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_rc4.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_rc4_hmac_md5.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_rc4_hmac_md5_hw.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_rc4_hw.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_seed.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_seed_hw.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_sm4.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_sm4_ccm.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_sm4_ccm_hw.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_sm4_gcm.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_sm4_gcm_hw.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_sm4_hw.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_sm4_xts.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_sm4_xts_hw.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_tdes.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_tdes_common.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_tdes_default.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_tdes_default_hw.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_tdes_hw.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_tdes_wrap.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/cipher_tdes_wrap_hw.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/ciphercommon.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/ciphercommon_block.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/ciphercommon_ccm.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/ciphercommon_ccm_hw.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/ciphercommon_gcm.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/ciphercommon_gcm_hw.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/ciphers/ciphercommon_hw.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/digests/blake2_prov.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/digests/blake2b_prov.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/digests/blake2s_prov.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/digests/digestcommon.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/digests/md4_prov.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/digests/md5_prov.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/digests/md5_sha1_prov.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/digests/mdc2_prov.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/digests/null_prov.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/digests/ripemd_prov.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/digests/sha2_prov.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/digests/sha3_prov.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/digests/sm3_prov.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/digests/wp_prov.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/encode_decode/decode_der2key.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/encode_decode/decode_epki2pki.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/encode_decode/decode_msblob2key.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/encode_decode/decode_pem2der.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/encode_decode/decode_pvk2key.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/encode_decode/decode_spki2typespki.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/encode_decode/encode_key2any.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/encode_decode/encode_key2blob.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/encode_decode/encode_key2ms.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/encode_decode/encode_key2text.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/encode_decode/endecoder_common.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/exchange/dh_exch.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/exchange/ecdh_exch.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/exchange/ecx_exch.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/exchange/kdf_exch.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/kdfs/argon2.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/kdfs/hkdf.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/kdfs/hmacdrbg_kdf.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/kdfs/kbkdf.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/kdfs/krb5kdf.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/kdfs/pbkdf1.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/kdfs/pbkdf2.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/kdfs/pbkdf2_fips.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/kdfs/pkcs12kdf.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/kdfs/pvkkdf.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/kdfs/scrypt.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/kdfs/sshkdf.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/kdfs/sskdf.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/kdfs/tls1_prf.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/kdfs/x942kdf.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/kem/ec_kem.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/kem/ecx_kem.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/kem/kem_util.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/kem/rsa_kem.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/keymgmt/dh_kmgmt.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/keymgmt/dsa_kmgmt.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/keymgmt/ec_kmgmt.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/keymgmt/ecx_kmgmt.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/keymgmt/kdf_legacy_kmgmt.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/keymgmt/mac_legacy_kmgmt.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/keymgmt/rsa_kmgmt.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/macs/blake2b_mac.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/macs/blake2s_mac.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/macs/cmac_prov.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/macs/gmac_prov.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/macs/hmac_prov.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/macs/kmac_prov.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/macs/poly1305_prov.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/macs/siphash_prov.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/rands/crngt.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/rands/drbg.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/rands/drbg_ctr.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/rands/drbg_hash.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/rands/drbg_hmac.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/rands/seed_src.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/rands/seeding/rand_cpu_x86.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/rands/seeding/rand_tsc.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/rands/seeding/rand_unix.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/rands/seeding/rand_win.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/rands/test_rng.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/signature/dsa_sig.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/signature/ecdsa_sig.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/signature/eddsa_sig.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/signature/mac_legacy_sig.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/signature/rsa_sig.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/signature/sm2_sig.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/storemgmt/file_store.c
    ${OPENSSL_SOURCE_DIR}/providers/implementations/storemgmt/file_store_any2obj.c
    ${OPENSSL_SOURCE_DIR}/providers/nullprov.c
    ${OPENSSL_SOURCE_DIR}/providers/prov_running.c
    ${OPENSSL_SOURCE_DIR}/ssl/record/methods/tls_pad.c
    ${OPENSSL_SOURCE_DIR}/ssl/record/methods/ssl3_cbc.c
)

if(NOT ENABLE_OPENSSL_DYNAMIC)
    set(CRYPTO_SRC ${CRYPTO_SRC}
        ${OPENSSL_SOURCE_DIR}/providers/fips/fips_entry.c
        ${OPENSSL_SOURCE_DIR}/providers/fips/fipsprov.c
        ${OPENSSL_SOURCE_DIR}/providers/legacyprov.c
    )
endif()

if(ARCH_AMD64)
    if (OS_DARWIN)
        set(CRYPTO_SRC ${CRYPTO_SRC}
            ${OPENSSL_SOURCE_DIR}/crypto/aes/aes_cbc.c
            ${OPENSSL_SOURCE_DIR}/crypto/aes/aes_core.c
            ${OPENSSL_SOURCE_DIR}/crypto/bn/bn_asm.c
            ${OPENSSL_SOURCE_DIR}/crypto/chacha/chacha_enc.c
            ${OPENSSL_SOURCE_DIR}/crypto/sha/keccak1600.c
            ${OPENSSL_SOURCE_DIR}/crypto/whrlpool/wp_block.c
            ${OPENSSL_SOURCE_DIR}/crypto/camellia/camellia.c
            ${OPENSSL_SOURCE_DIR}/crypto/camellia/cmll_cbc.c
            ${OPENSSL_SOURCE_DIR}/crypto/mem_clr.c
            ${OPENSSL_SOURCE_DIR}/crypto/rc4/rc4_enc.c
            ${OPENSSL_SOURCE_DIR}/crypto/rc4/rc4_skey.c
        )
    else()
        set(CRYPTO_SRC ${CRYPTO_SRC}
            ${OPENSSL_SOURCE_DIR}/crypto/bn/asm/x86_64-gcc.c
            ${OPENSSL_SOURCE_DIR}/crypto/bn/rsaz_exp.c
            asm/crypto/aes/aes-x86_64.s
            asm/crypto/aes/aesni-mb-x86_64.s
            asm/crypto/aes/aesni-sha1-x86_64.s
            asm/crypto/aes/aesni-sha256-x86_64.s
            asm/crypto/aes/aesni-x86_64.s
            asm/crypto/aes/bsaes-x86_64.s
            asm/crypto/aes/vpaes-x86_64.s
            asm/crypto/bn/rsaz-2k-avx512.s
            asm/crypto/bn/rsaz-3k-avx512.s
            asm/crypto/bn/rsaz-4k-avx512.s
            asm/crypto/bn/rsaz-avx2.s
            asm/crypto/bn/rsaz-x86_64.s
            ${OPENSSL_SOURCE_DIR}/crypto/bn/rsaz_exp_x2.c
            asm/crypto/bn/x86_64-gf2m.s
            asm/crypto/bn/x86_64-mont.s
            asm/crypto/bn/x86_64-mont5.s
            asm/crypto/camellia/cmll-x86_64.s
            asm/crypto/chacha/chacha-x86_64.s
            asm/crypto/ec/ecp_nistz256-x86_64.s
            ${OPENSSL_SOURCE_DIR}/crypto/ec/ecp_nistz256.c
            asm/crypto/ec/x25519-x86_64.s
            asm/crypto/x86_64cpuid.s
            asm/crypto/md5/md5-x86_64.s
            asm/crypto/modes/aesni-gcm-x86_64.s
            asm/crypto/modes/aes-gcm-avx512.s
            asm/crypto/modes/ghash-x86_64.s
            asm/crypto/poly1305/poly1305-x86_64.s
            asm/crypto/rc4/rc4-md5-x86_64.s
            asm/crypto/rc4/rc4-x86_64.s
            asm/crypto/sha/keccak1600-x86_64.s
            asm/crypto/sha/sha1-mb-x86_64.s
            asm/crypto/sha/sha1-x86_64.s
            asm/crypto/sha/sha256-mb-x86_64.s
            asm/crypto/sha/sha256-x86_64.s
            asm/crypto/sha/sha512-x86_64.s
            asm/crypto/whrlpool/wp-x86_64.s
        )
    endif()
elseif(ARCH_AARCH64)
    if (OS_DARWIN)
        set(CRYPTO_SRC ${CRYPTO_SRC}
            ${OPENSSL_SOURCE_DIR}/crypto/aes/aes_cbc.c
            ${OPENSSL_SOURCE_DIR}/crypto/aes/aes_core.c
            ${OPENSSL_SOURCE_DIR}/crypto/bn/bn_asm.c
            ${OPENSSL_SOURCE_DIR}/crypto/chacha/chacha_enc.c
            ${OPENSSL_SOURCE_DIR}/crypto/sha/keccak1600.c
            ${OPENSSL_SOURCE_DIR}/crypto/whrlpool/wp_block.c
            ${OPENSSL_SOURCE_DIR}/crypto/camellia/camellia.c
            ${OPENSSL_SOURCE_DIR}/crypto/camellia/cmll_cbc.c
            ${OPENSSL_SOURCE_DIR}/crypto/mem_clr.c
            ${OPENSSL_SOURCE_DIR}/crypto/rc4/rc4_enc.c
            ${OPENSSL_SOURCE_DIR}/crypto/rc4/rc4_skey.c
        )
    else()
        set(CRYPTO_SRC ${CRYPTO_SRC}
            ${OPENSSL_SOURCE_DIR}/crypto/aes/aes_cbc.c
            ${OPENSSL_SOURCE_DIR}/crypto/aes/aes_core.c
            ${OPENSSL_SOURCE_DIR}/crypto/bn/bn_asm.c
            ${OPENSSL_SOURCE_DIR}/crypto/bn/rsaz_exp.c
            ${OPENSSL_SOURCE_DIR}/crypto/bn/rsaz_exp_x2.c
            ${OPENSSL_SOURCE_DIR}/crypto/camellia/camellia.c
            ${OPENSSL_SOURCE_DIR}/crypto/camellia/cmll_cbc.c
            ${OPENSSL_SOURCE_DIR}/crypto/armcap.c
            ${OPENSSL_SOURCE_DIR}/crypto/rc4/rc4_enc.c
            ${OPENSSL_SOURCE_DIR}/crypto/rc4/rc4_skey.c
            ${OPENSSL_SOURCE_DIR}/crypto/whrlpool/wp_block.c
            asm/crypto/aes/aesv8-armx.S
            asm/crypto/aes/vpaes-armv8.S
            asm/crypto/bn/armv8-mont.S
            asm/crypto/chacha/chacha-armv8.S
            asm/crypto/ec/ecp_nistz256-armv8.S
            ${OPENSSL_SOURCE_DIR}/crypto/ec/ecp_nistz256.c
            ${OPENSSL_SOURCE_DIR}/crypto/ec/ecp_sm2p256.c
            ${OPENSSL_SOURCE_DIR}/crypto/ec/ecp_sm2p256_table.c
            asm/crypto/arm64cpuid.S
            asm/crypto/modes/ghashv8-armx.S
            asm/crypto/poly1305/poly1305-armv8.S
            asm/crypto/sha/keccak1600-armv8.S
            asm/crypto/sha/sha1-armv8.S
            asm/crypto/sha/sha256-armv8.S
            asm/crypto/sha/sha512-armv8.S
            asm/crypto/modes/asm/aes-gcm-armv8_64.S
            asm/crypto/sm4/asm/sm4-armv8.S
            asm/crypto/sm4/asm/vpsm4-armv8.S
            asm/crypto/md5/asm/md5-aarch64.S
            asm/crypto/aes/bsaes-armv8.S
            asm/crypto/chacha/chacha-armv8-sve.S
            asm/crypto/ec/ecp_sm2p256-armv8.S
            asm/crypto/modes/asm/aes-gcm-armv8-unroll8_64.S
            asm/crypto/sm3/asm/sm3-armv8.S
            asm/crypto/sm4/asm/vpsm4_ex-armv8.S

            ${PLATFORM_DIRECTORY}/params_idx.c
        )
    endif()
elseif(ARCH_PPC64LE)
    set(CRYPTO_SRC ${CRYPTO_SRC}
        asm/crypto/modes/ghashp8-ppc.s
        asm/crypto/aes/aesp8-ppc.s
        asm/crypto/ppccpuid.s
        ${OPENSSL_SOURCE_DIR}/crypto/aes/aes_core.c
        ${OPENSSL_SOURCE_DIR}/crypto/aes/aes_cbc.c
        ${OPENSSL_SOURCE_DIR}/crypto/bn/bn_asm.c
        ${OPENSSL_SOURCE_DIR}/crypto/camellia/camellia.c
        ${OPENSSL_SOURCE_DIR}/crypto/camellia/cmll_cbc.c
        ${OPENSSL_SOURCE_DIR}/crypto/chacha/chacha_enc.c
        ${OPENSSL_SOURCE_DIR}/crypto/rc4/rc4_enc.c
        ${OPENSSL_SOURCE_DIR}/crypto/rc4/rc4_skey.c
        ${OPENSSL_SOURCE_DIR}/crypto/sha/keccak1600.c
        ${OPENSSL_SOURCE_DIR}/crypto/whrlpool/wp_block.c
        ${OPENSSL_SOURCE_DIR}/engines/e_afalg.c
        ${OPENSSL_SOURCE_DIR}/crypto/ppccap.c
        asm/crypto/modes/aes-gcm-ppc.s
    )
elseif(ARCH_S390X)
    set(CRYPTO_SRC ${CRYPTO_SRC}
        asm/crypto/aes/aes-s390x.S
        asm/crypto/s390xcpuid.S
        ${OPENSSL_SOURCE_DIR}/crypto/bn/asm/s390x.S
        ${OPENSSL_SOURCE_DIR}/crypto/s390xcap.c
        ${OPENSSL_SOURCE_DIR}/crypto/bn/bn_s390x.c
        ${OPENSSL_SOURCE_DIR}/crypto/camellia/camellia.c
        ${OPENSSL_SOURCE_DIR}/crypto/camellia/cmll_cbc.c
        asm/crypto/chacha/chacha-s390x.S
        asm/crypto/rc4/rc4-s390x.S
        asm/crypto/sha/keccak1600-s390x.S
        ${OPENSSL_SOURCE_DIR}/crypto/whrlpool/wp_block.c
    )
elseif(ARCH_RISCV64)
    set(CRYPTO_SRC ${CRYPTO_SRC}
        asm/crypto/aes/aes-riscv64-zkn.S
        asm/crypto/modes/ghash-riscv64.S
        asm/crypto/riscv64cpuid.S
        ${OPENSSL_SOURCE_DIR}/crypto/aes/aes_cbc.c
        ${OPENSSL_SOURCE_DIR}/crypto/aes/aes_core.c
        ${OPENSSL_SOURCE_DIR}/crypto/bn/bn_asm.c
        ${OPENSSL_SOURCE_DIR}/crypto/chacha/chacha_enc.c
        ${OPENSSL_SOURCE_DIR}/crypto/sha/keccak1600.c
        ${OPENSSL_SOURCE_DIR}/crypto/whrlpool/wp_block.c
        ${OPENSSL_SOURCE_DIR}/crypto/whrlpool/wp_dgst.c
        ${OPENSSL_SOURCE_DIR}/crypto/ec/ecp_nistz256.c
        ${OPENSSL_SOURCE_DIR}/crypto/camellia/camellia.c
        ${OPENSSL_SOURCE_DIR}/crypto/camellia/cmll_cbc.c
        ${OPENSSL_SOURCE_DIR}/crypto/mem_clr.c
        ${OPENSSL_SOURCE_DIR}/crypto/rc4/rc4_enc.c
        ${OPENSSL_SOURCE_DIR}/crypto/rc4/rc4_skey.c
        ${OPENSSL_SOURCE_DIR}/crypto/riscvcap.c
    )
elseif(ARCH_LOONGARCH64)
    set(CRYPTO_SRC ${CRYPTO_SRC}
        asm/crypto/loongarch64cpuid.S
        ${OPENSSL_SOURCE_DIR}/crypto/aes/aes_cbc.c
        ${OPENSSL_SOURCE_DIR}/crypto/aes/aes_core.c
        ${OPENSSL_SOURCE_DIR}/crypto/bn/bn_asm.c
        ${OPENSSL_SOURCE_DIR}/crypto/chacha/chacha_enc.c
        ${OPENSSL_SOURCE_DIR}/crypto/sha/keccak1600.c
        ${OPENSSL_SOURCE_DIR}/crypto/whrlpool/wp_block.c
        ${OPENSSL_SOURCE_DIR}/crypto/whrlpool/wp_dgst.c
        ${OPENSSL_SOURCE_DIR}/crypto/ec/ecp_nistz256.c
        ${OPENSSL_SOURCE_DIR}/crypto/camellia/camellia.c
        ${OPENSSL_SOURCE_DIR}/crypto/camellia/cmll_cbc.c
        ${OPENSSL_SOURCE_DIR}/crypto/mem_clr.c
        ${OPENSSL_SOURCE_DIR}/crypto/rc4/rc4_enc.c
        ${OPENSSL_SOURCE_DIR}/crypto/rc4/rc4_skey.c
        ${OPENSSL_SOURCE_DIR}/crypto/loongarchcap.c
    )
endif()

set(SSL_SRC
    ${OPENSSL_SOURCE_DIR}/ssl/bio_ssl.c
    ${OPENSSL_SOURCE_DIR}/ssl/d1_lib.c
    ${OPENSSL_SOURCE_DIR}/ssl/d1_msg.c
    ${OPENSSL_SOURCE_DIR}/ssl/d1_srtp.c
    ${OPENSSL_SOURCE_DIR}/ssl/event_queue.c
    ${OPENSSL_SOURCE_DIR}/ssl/methods.c
    ${OPENSSL_SOURCE_DIR}/ssl/pqueue.c
    ${OPENSSL_SOURCE_DIR}/ssl/priority_queue.c
    ${OPENSSL_SOURCE_DIR}/ssl/s3_enc.c
    ${OPENSSL_SOURCE_DIR}/ssl/s3_lib.c
    ${OPENSSL_SOURCE_DIR}/ssl/s3_msg.c
    ${OPENSSL_SOURCE_DIR}/ssl/ssl_asn1.c
    ${OPENSSL_SOURCE_DIR}/ssl/ssl_cert.c
    ${OPENSSL_SOURCE_DIR}/ssl/ssl_cert_comp.c
    ${OPENSSL_SOURCE_DIR}/ssl/ssl_ciph.c
    ${OPENSSL_SOURCE_DIR}/ssl/ssl_conf.c
    ${OPENSSL_SOURCE_DIR}/ssl/ssl_err.c
    ${OPENSSL_SOURCE_DIR}/ssl/ssl_err_legacy.c
    ${OPENSSL_SOURCE_DIR}/ssl/ssl_init.c
    ${OPENSSL_SOURCE_DIR}/ssl/ssl_lib.c
    ${OPENSSL_SOURCE_DIR}/ssl/ssl_mcnf.c
    ${OPENSSL_SOURCE_DIR}/ssl/ssl_rsa.c
    ${OPENSSL_SOURCE_DIR}/ssl/ssl_sess.c
    ${OPENSSL_SOURCE_DIR}/ssl/ssl_stat.c
    ${OPENSSL_SOURCE_DIR}/ssl/ssl_txt.c
    ${OPENSSL_SOURCE_DIR}/ssl/ssl_utst.c
    ${OPENSSL_SOURCE_DIR}/ssl/t1_enc.c
    ${OPENSSL_SOURCE_DIR}/ssl/t1_lib.c
    ${OPENSSL_SOURCE_DIR}/ssl/t1_trce.c
    ${OPENSSL_SOURCE_DIR}/ssl/tls13_enc.c
    ${OPENSSL_SOURCE_DIR}/ssl/tls_depr.c
    ${OPENSSL_SOURCE_DIR}/ssl/quic/cc_newreno.c
    ${OPENSSL_SOURCE_DIR}/ssl/quic/quic_ackm.c
    ${OPENSSL_SOURCE_DIR}/ssl/quic/quic_cfq.c
    ${OPENSSL_SOURCE_DIR}/ssl/quic/quic_channel.c
    ${OPENSSL_SOURCE_DIR}/ssl/quic/quic_demux.c
    ${OPENSSL_SOURCE_DIR}/ssl/quic/quic_fc.c
    ${OPENSSL_SOURCE_DIR}/ssl/quic/quic_fifd.c
    ${OPENSSL_SOURCE_DIR}/ssl/quic/quic_impl.c
    ${OPENSSL_SOURCE_DIR}/ssl/quic/quic_method.c
    ${OPENSSL_SOURCE_DIR}/ssl/quic/quic_reactor.c
    ${OPENSSL_SOURCE_DIR}/ssl/quic/quic_record_rx.c
    ${OPENSSL_SOURCE_DIR}/ssl/quic/quic_record_shared.c
    ${OPENSSL_SOURCE_DIR}/ssl/quic/quic_record_tx.c
    ${OPENSSL_SOURCE_DIR}/ssl/quic/quic_record_util.c
    ${OPENSSL_SOURCE_DIR}/ssl/quic/quic_rstream.c
    ${OPENSSL_SOURCE_DIR}/ssl/quic/quic_rx_depack.c
    ${OPENSSL_SOURCE_DIR}/ssl/quic/quic_sf_list.c
    ${OPENSSL_SOURCE_DIR}/ssl/quic/quic_sstream.c
    ${OPENSSL_SOURCE_DIR}/ssl/quic/quic_statm.c
    ${OPENSSL_SOURCE_DIR}/ssl/quic/quic_stream_map.c
    ${OPENSSL_SOURCE_DIR}/ssl/quic/quic_thread_assist.c
    ${OPENSSL_SOURCE_DIR}/ssl/quic/quic_tls.c
    ${OPENSSL_SOURCE_DIR}/ssl/quic/quic_trace.c
    ${OPENSSL_SOURCE_DIR}/ssl/quic/quic_tserver.c
    ${OPENSSL_SOURCE_DIR}/ssl/quic/quic_txp.c
    ${OPENSSL_SOURCE_DIR}/ssl/quic/quic_txpim.c
    ${OPENSSL_SOURCE_DIR}/ssl/quic/quic_wire.c
    ${OPENSSL_SOURCE_DIR}/ssl/quic/quic_wire_pkt.c
    ${OPENSSL_SOURCE_DIR}/ssl/quic/uint_set.c
    ${OPENSSL_SOURCE_DIR}/ssl/record/rec_layer_d1.c
    ${OPENSSL_SOURCE_DIR}/ssl/record/rec_layer_s3.c
    ${OPENSSL_SOURCE_DIR}/ssl/record/methods/dtls_meth.c
    ${OPENSSL_SOURCE_DIR}/ssl/record/methods/ssl3_meth.c
    ${OPENSSL_SOURCE_DIR}/ssl/record/methods/tls13_meth.c
    ${OPENSSL_SOURCE_DIR}/ssl/record/methods/tls1_meth.c
    ${OPENSSL_SOURCE_DIR}/ssl/record/methods/tls_common.c
    ${OPENSSL_SOURCE_DIR}/ssl/record/methods/tls_multib.c
    ${OPENSSL_SOURCE_DIR}/ssl/record/methods/tlsany_meth.c
    ${OPENSSL_SOURCE_DIR}/ssl/statem/extensions.c
    ${OPENSSL_SOURCE_DIR}/ssl/statem/extensions_clnt.c
    ${OPENSSL_SOURCE_DIR}/ssl/statem/extensions_cust.c
    ${OPENSSL_SOURCE_DIR}/ssl/statem/extensions_srvr.c
    ${OPENSSL_SOURCE_DIR}/ssl/statem/statem.c
    ${OPENSSL_SOURCE_DIR}/ssl/statem/statem_clnt.c
    ${OPENSSL_SOURCE_DIR}/ssl/statem/statem_dtls.c
    ${OPENSSL_SOURCE_DIR}/ssl/statem/statem_lib.c
    ${OPENSSL_SOURCE_DIR}/ssl/statem/statem_srvr.c
)

# Disable all deprecated API
#
# Engine API is deprecated in OpenSSL 3.0.
# https://docs.openssl.org/3.3/man3/ENGINE_add/#history
#
# SRP API is deprecated in OpenSSL 3.0.
# https://docs.openssl.org/3.1/man3/SRP_Calc_B/#history
add_definitions(-DOPENSSL_NO_DEPRECATED)
add_definitions(-DOPENSSL_NO_ENGINE)
add_definitions(-DOPENSSL_NO_SRP)

if(ENABLE_OPENSSL_DYNAMIC)
    add_library(crypto SHARED ${CRYPTO_SRC})
    set_target_properties(crypto PROPERTIES VERSION "${LIB_VERSION}" SOVERSION "${LIB_SOVERSION}")
    set_target_properties(crypto PROPERTIES LIBRARY_OUTPUT_DIRECTORY ${PROJECT_BINARY_DIR}/programs)

    add_library(ssl SHARED ${SSL_SRC})
    set_target_properties(ssl PROPERTIES VERSION "${LIB_VERSION}" SOVERSION "${LIB_SOVERSION}")
    set_target_properties(ssl PROPERTIES LIBRARY_OUTPUT_DIRECTORY ${PROJECT_BINARY_DIR}/programs)
else()
    # Enable legacy crypto support for OpenSSL 3.+
    # to avoid `dlopen(legacy.so)`.
    add_definitions(-DSTATIC_LEGACY)

    add_library(crypto ${CRYPTO_SRC})
    add_library(ssl ${SSL_SRC})
endif()


target_include_directories(crypto
    SYSTEM PUBLIC "${PLATFORM_DIRECTORY}/include"
    PRIVATE "${PLATFORM_DIRECTORY}/include_private")

target_include_directories(crypto
    SYSTEM PUBLIC ${OPENSSL_SOURCE_DIR}/include
    PRIVATE ${OPENSSL_SOURCE_DIR}/providers/common/include
    PRIVATE ${OPENSSL_SOURCE_DIR}/providers/implementations/include
    PRIVATE ${OPENSSL_SOURCE_DIR}/crypto
    PRIVATE ${OPENSSL_SOURCE_DIR}/crypto/include
    PRIVATE ${OPENSSL_SOURCE_DIR}/crypto/ec/curve448
    PRIVATE ${OPENSSL_SOURCE_DIR}/crypto/ec/curve448/arch_32
    PRIVATE ${OPENSSL_SOURCE_DIR}/crypto/ec/curve448/arch_64
    PRIVATE ${OPENSSL_SOURCE_DIR})

target_include_directories(ssl
    PRIVATE ${OPENSSL_SOURCE_DIR})

target_link_libraries(ssl crypto)

add_library(OpenSSL::Crypto ALIAS crypto)
add_library(OpenSSL::SSL ALIAS ssl)

if(OPENSSL_AUX_BUILD_FOR_CROSS_COMPILATION)
    install(DIRECTORY "${PLATFORM_DIRECTORY}/include" DESTINATION "${CMAKE_BINARY_DIR}")
    install(DIRECTORY "${OPENSSL_SOURCE_DIR}/include" DESTINATION "${CMAKE_BINARY_DIR}")
else()
    install(FILES openssl.conf fipsmodule.conf DESTINATION "${CLICKHOUSE_ETC_DIR}/clickhouse-server" COMPONENT clickhouse)
endif()
