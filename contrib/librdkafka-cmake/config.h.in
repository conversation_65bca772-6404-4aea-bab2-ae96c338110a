// Automatically generated by ./configure
///	NOTE: Version 2.8.0 was used to generate and manually modified after that. Therefore this should be fine
/// until we upgrade to something newer than that.
/// Commented out the followings:
/// - #define ARCH "x86_64": we build on multiple archs
/// - ENABLE_XXX: the commented out ones are only used in librdkafka's configure and CMake, but not in source
///   NOTE: ENABLE_DEVEL and ENABLE_REFCNT_DEBUG is used in the source at the time of writing
///
/// Commented out the following to disable them:
/// - ENABLE_C11THREADS: to maintain compatibility with old libc, maybe not necessary anymore
/// - WITH_GCC
/// - WITH_GXX
/// - WITH_INSTALL
/// - HAS_GNU_AR
/// - HAVE_PIC
/// - WITH_GNULD
/// - WITH_C11THREADS
/// - HAVE_PYTHON
///
/// Modified the follwoings:
/// - WITH_{CURL,SASL_CYRUS,SASL_OAUTHBEARER,<PERSON><PERSON>_SCRAM}: made them CMake dependant
/// - BUI<PERSON>_WITH: tried to make some sense of it, don't spend to much time on it
///
/// Added:
/// - special handling of __APPLE__

#ifndef _CONFIG_H_
#define _CONFIG_H_
// BUILT_WITH
#define BUILT_WITH "ZLIB ZSTD LZ4_EXT SNAPPY SSL SASL_CYRUS SASL_SCRAM SASL_OAUTHBEARER"

// distro
#define SOLIB_EXT ".so"
//#define ARCH "x86_64"
#define CPU "generic"
#define WITHOUT_OPTIMIZATION 0
#define WITH_STRIP 0
#define ENABLE_DEVEL 0
#define ENABLE_VALGRIND 0
#define ENABLE_REFCNT_DEBUG 0

// #define ENABLE_ZLIB 1
// #define ENABLE_ZSTD 1
// #define ENABLE_SSL 1
// #define ENABLE_GSSAPI 1
// #define ENABLE_LZ4_EXT 1
// #define ENABLE_REGEX_EXT 1
// #define ENABLE_C11THREADS "try"
// #define ENABLE_SYSLOG 1
#define MKL_APP_NAME "librdkafka"
#define MKL_APP_DESC_ONELINE "The Apache Kafka C/C++ library"
// gcc
// #define WITH_GCC 1
// gxx
// #define WITH_GXX 1
// install
// #define WITH_INSTALL 1
// gnuar
// #define HAS_GNU_AR 1
// PIC
// #define HAVE_PIC 1
// gnulib
// #define WITH_GNULD 1
// __atomic_32
#define HAVE_ATOMICS_32 1
// __atomic_32
#define HAVE_ATOMICS_32_ATOMIC 1
// atomic_32
#define ATOMIC_OP32(OP1,OP2,PTR,VAL) __atomic_ ## OP1 ## _ ## OP2(PTR, VAL, __ATOMIC_SEQ_CST)
// __atomic_64
#define HAVE_ATOMICS_64 1
// __atomic_64
#define HAVE_ATOMICS_64_ATOMIC 1
// atomic_64
#define ATOMIC_OP64(OP1,OP2,PTR,VAL) __atomic_ ## OP1 ## _ ## OP2(PTR, VAL, __ATOMIC_SEQ_CST)
// atomic_64
#define ATOMIC_OP(OP1,OP2,PTR,VAL) __atomic_ ## OP1 ## _ ## OP2(PTR, VAL, __ATOMIC_SEQ_CST)
// parseversion
#define RDKAFKA_VERSION_STR "2.8.0"
// parseversion
#define MKL_APP_VERSION "2.8.0"
// disable C11 threads for compatibility with old libc, also C11 threads are condireder harmful
#define WITH_C11THREADS 0
// WITH_PLUGINS - see rd_kafka_plugin_new: it allow to dload some external plugins (no plans to support it in ClickHouse)
#define WITH_PLUGINS 0
// libdl
#define WITH_LIBDL 0
// zlib
#define WITH_ZLIB 1
// libzstd
#define WITH_ZSTD 1
// WITH_HDRHISTOGRAM - not used so far, can be useful for better stats
// #define WITH_HDRHISTOGRAM 1
// syslog
//#define WITH_SYSLOG 1
// WITH_SNAPPY
#define WITH_SNAPPY 1
// WITH_SOCKEM - socket emulation (used for testing)
//#define WITH_SOCKEM 1
// libssl
#cmakedefine WITH_SSL 1
// WITH_SASL_SCRAM
#cmakedefine WITH_SASL_SCRAM 1
// WITH_SASL_OAUTHBEARER
#cmakedefine WITH_SASL_OAUTHBEARER 1
#cmakedefine WITH_OAUTHBEARER_OIDC 1
// libsasl2
#cmakedefine WITH_SASL_CYRUS 1
// WITH_CURL
#cmakedefine WITH_CURL 1
// crc32chw
#if !defined(__PPC__) && !defined(__riscv) && !defined(__aarch64__) && !defined(__s390x__) && !defined(__loongarch64)
#define WITH_CRC32C_HW 1
#endif
// regex
#define HAVE_REGEX 1
// rand_r
#define HAVE_RAND_R 1
// strndup
#define HAVE_STRNDUP 1
// strerror_r
#define HAVE_STRERROR_R 1
// strcasestr
#define HAVE_STRCASESTR 1
#ifdef __APPLE__
#define HAVE_PTHREAD_SETNAME_DARWIN 1
#if (__ENVIRONMENT_MAC_OS_X_VERSION_MIN_REQUIRED__ <= 101400)
#define _TTHREAD_EMULATE_TIMESPEC_GET_
#endif
#elif defined(__FreeBSD__)
#define HAVE_PTHREAD_SETNAME_FREEBSD 1
#else
// pthread_setname_gnu
#define HAVE_PTHREAD_SETNAME_GNU 1
#endif
// python3
//#define HAVE_PYTHON 1
// getrusage - used in a single test tests/rusage.c only
//#define HAVE_GETRUSAGE 1
#endif /* _CONFIG_H_ */
