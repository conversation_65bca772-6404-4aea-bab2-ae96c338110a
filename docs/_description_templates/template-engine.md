# EngineName {#enginename}

-   What the Database/Table engine does.
-   Relations with other engines if they exist.

## Creating a Database {#creating-a-database}
```sql
    CREATE DATABASE ...
```
or

## Creating a Table {#creating-a-table}
```sql
    CREATE TABLE ...
```

**Engine Parameters**

**Query Clauses** (for Table engines only)

## Virtual columns {#virtual-columns} (for Table engines only)

List and virtual columns with description, if they exist.

## Data Types Support {#data_types-support} (for Database engines only)

|  EngineName           | ClickHouse                         |
|-----------------------|------------------------------------|
| NativeDataTypeName    | [ClickHouseDataTypeName](link#)    |


## Specifics and recommendations {#specifics-and-recommendations}

Algorithms
Specifics of read and write processes
Examples of tasks
Recommendations for usage
Specifics of data storage

## Usage Example {#usage-example}

The example must show usage and use cases. The following text contains the recommended parts of this section.

Input table:

```text
```

Query:

```sql
```

Result:

```text
```

Follow up with any text to clarify the example.

**See Also**

-   [link](#)
