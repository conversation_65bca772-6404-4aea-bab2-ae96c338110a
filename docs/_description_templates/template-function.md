## functionName {#functionname-in-lower-case}

Short description.

**Syntax** (without SELECT)

```sql
<function syntax>
```

Alias: `<alias name>`. (Optional)

More text (Optional).

**Arguments** (Optional)

-   `x` — Description. Optional (only for optional arguments). Possible values: <values list>. Default value: <value>. [Type name](relative/path/to/type/dscr.md#type).
-   `y` — Description. Optional (only for optional arguments). Possible values: <values list>.Default value: <value>. [Type name](relative/path/to/type/dscr.md#type).

**Parameters** (Optional, only for parametric aggregate functions)

-   `z` — Description. Optional (only for optional parameters). Possible values: <values list>. Default value: <value>. [Type name](relative/path/to/type/dscr.md#type).

**Returned value(s)**

-   Returned values list.

Type: [Type name](relative/path/to/type/dscr.md#type).

**Example**

The example must show usage and/or a use cases. The following text contains recommended parts of an example.

Input table (Optional):

```text
```

Query:

```sql
```

Result:

```text
```

**See Also** (Optional)

-   [link](#)
