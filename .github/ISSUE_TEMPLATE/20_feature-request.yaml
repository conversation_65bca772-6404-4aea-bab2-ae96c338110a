name: Feature request
description: Suggest an idea for ClickHouse
labels: ["feature"]
body:
  - type: markdown
    attributes:
      value: |
        > (you don't have to strictly follow this form)
  - type: textarea
    attributes:
      label: Company or project name
      description: Put your company name or project description here.
    validations:
      required: false
  - type: textarea
    attributes:
      label: Use case
      description: A clear and concise description of what the intended usage scenario is.
    validations:
      required: true
  - type: textarea
    attributes:
      label: Describe the solution you'd like
      description: A clear and concise description of what you want to happen.
    validations:
      required: true
  - type: textarea
    attributes:
      label: Describe alternatives you've considered
      description: A clear and concise description of any alternative solutions or features you've considered.
    validations:
      required: false
  - type: textarea
    attributes:
      label: Additional context
      description: Add any other context or screenshots about the feature request here.
    validations:
      required: false
