name: Performance issue
description: Report something working slower than expected
labels: ["performance"]
body:
  - type: markdown
    attributes:
      value: |
        > (you don't have to strictly follow this form)
  - type: textarea
    attributes:
      label: Company or project name
      description: Put your company name or project description here.
    validations:
      required: false
  - type: textarea
    attributes:
      label: Describe the situation
      description: What exactly works slower than expected?
    validations:
      required: true
  - type: textarea
    attributes:
      label: Which ClickHouse versions are affected?
    validations:
      required: true
  - type: textarea
    attributes:
      label: How to reproduce
      description: |
        * Which interface to use, if matters
        * Non-default settings, if any
        * `CREATE TABLE` statements for all tables involved
        * Sample data for all these tables, use [clickhouse-obfuscator](https://github.com/ClickHouse/ClickHouse/blob/c81bec37a58757be1e2b1ac6f20a62b3f14a31f1/programs/obfuscator/Obfuscator.cpp#L55-L95) if necessary
        * Queries to run that lead to unexpected result
    validations:
      required: true
  - type: textarea
    attributes:
      label: Expected performance
      description: What are your performance expectation, why do you think they are realistic? Has it been working faster in older ClickHouse releases? Is it working faster in some specific other system?
    validations:
      required: false
  - type: textarea
    attributes:
      label: Additional context
      description: Add any other context about the problem here.
    validations:
      required: false
