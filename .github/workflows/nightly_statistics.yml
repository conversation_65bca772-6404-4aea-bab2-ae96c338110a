# generated by praktika

name: NightlyStatistics
on:
  schedule:
    - cron: 13 5 * * *
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}

env:
  PYTHONUNBUFFERED: 1
  CHECKOUT_REF: ""

jobs:

  config_workflow:
    runs-on: [self-hosted, style-checker-aarch64]
    needs: []
    name: "Config Workflow"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:

          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJ<PERSON>(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Config Workflow' --workflow "NightlyStatistics" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Config Workflow' --workflow "NightlyStatistics" --ci |& tee ./ci/tmp/job.log
          fi

  collect_statistics:
    runs-on: [self-hosted, style-checker-aarch64]
    needs: [config_workflow]
    name: "Collect Statistics"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:

          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Collect Statistics' --workflow "NightlyStatistics" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Collect Statistics' --workflow "NightlyStatistics" --ci |& tee ./ci/tmp/job.log
          fi
