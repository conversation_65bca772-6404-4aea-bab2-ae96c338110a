# generated by praktika

name: PR

on:
  pull_request:
    branches: ['master']

env:
  # Force the stdout and stderr streams to be unbuffered
  PYTHONUNBUFFERED: 1
  DISABLE_CI_MERGE_COMMIT: ${{ vars.DISABLE_CI_MERGE_COMMIT || '0' }}
  DISABLE_CI_CACHE: ${{ vars.DISABLE_CI_CACHE || '0' }}
  CHECKOUT_REF: ${{ vars.DISABLE_CI_MERGE_COMMIT == '1' && github.event.pull_request.head.sha || '' }}

# Allow updating GH commit statuses and PR comments to post an actual job reports link
permissions: write-all

jobs:

  config_workflow:
    runs-on: [self-hosted, style-checker-aarch64]
    needs: []
    name: "Config Workflow"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Config Workflow' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Config Workflow' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  dockers_build_amd:
    runs-on: [self-hosted, style-checker]
    needs: [config_workflow]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'RG9ja2VycyBCdWlsZCAoYW1kKQ==') }}
    name: "Dockers Build (amd)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Dockers Build (amd)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Dockers Build (amd)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  dockers_build_arm:
    runs-on: [self-hosted, style-checker-aarch64]
    needs: [config_workflow]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'RG9ja2VycyBCdWlsZCAoYXJtKQ==') }}
    name: "Dockers Build (arm)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Dockers Build (arm)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Dockers Build (arm)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  style_check:
    runs-on: [self-hosted, style-checker-aarch64]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'U3R5bGUgY2hlY2s=') }}
    name: "Style check"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Style check' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Style check' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  docs_check:
    runs-on: [self-hosted, func-tester-aarch64]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, style_check, build_arm_binary]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'RG9jcyBjaGVjaw==') }}
    name: "Docs check"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Docs check' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Docs check' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  fast_test:
    runs-on: [self-hosted, builder]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'RmFzdCB0ZXN0') }}
    name: "Fast test"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Fast test' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Fast test' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  build_amd_tidy:
    runs-on: [self-hosted, builder]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'QnVpbGQgKGFtZF90aWR5KQ==') }}
    name: "Build (amd_tidy)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Build (amd_tidy)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Build (amd_tidy)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  build_arm_tidy:
    runs-on: [self-hosted, builder-aarch64]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_tidy]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'QnVpbGQgKGFybV90aWR5KQ==') }}
    name: "Build (arm_tidy)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Build (arm_tidy)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Build (arm_tidy)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  build_amd_debug:
    runs-on: [self-hosted, builder]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, style_check, fast_test, build_amd_tidy]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'QnVpbGQgKGFtZF9kZWJ1Zyk=') }}
    name: "Build (amd_debug)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Build (amd_debug)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Build (amd_debug)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  build_amd_release:
    runs-on: [self-hosted, builder]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, style_check, fast_test, build_amd_tidy]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'QnVpbGQgKGFtZF9yZWxlYXNlKQ==') }}
    name: "Build (amd_release)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Build (amd_release)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Build (amd_release)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  build_amd_asan:
    runs-on: [self-hosted, builder]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, style_check, fast_test, build_amd_tidy]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'QnVpbGQgKGFtZF9hc2FuKQ==') }}
    name: "Build (amd_asan)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Build (amd_asan)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Build (amd_asan)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  build_amd_tsan:
    runs-on: [self-hosted, builder]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, style_check, fast_test, build_amd_tidy]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'QnVpbGQgKGFtZF90c2FuKQ==') }}
    name: "Build (amd_tsan)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Build (amd_tsan)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Build (amd_tsan)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  build_amd_msan:
    runs-on: [self-hosted, builder]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, style_check, fast_test, build_amd_tidy]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'QnVpbGQgKGFtZF9tc2FuKQ==') }}
    name: "Build (amd_msan)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Build (amd_msan)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Build (amd_msan)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  build_amd_ubsan:
    runs-on: [self-hosted, builder]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, style_check, fast_test, build_amd_tidy]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'QnVpbGQgKGFtZF91YnNhbik=') }}
    name: "Build (amd_ubsan)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Build (amd_ubsan)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Build (amd_ubsan)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  build_amd_binary:
    runs-on: [self-hosted, builder]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, style_check, fast_test, build_amd_tidy]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'QnVpbGQgKGFtZF9iaW5hcnkp') }}
    name: "Build (amd_binary)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Build (amd_binary)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Build (amd_binary)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  build_arm_release:
    runs-on: [self-hosted, builder-aarch64]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, style_check, fast_test, build_amd_tidy]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'QnVpbGQgKGFybV9yZWxlYXNlKQ==') }}
    name: "Build (arm_release)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Build (arm_release)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Build (arm_release)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  build_arm_asan:
    runs-on: [self-hosted, builder-aarch64]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, style_check, fast_test, build_amd_tidy]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'QnVpbGQgKGFybV9hc2FuKQ==') }}
    name: "Build (arm_asan)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Build (arm_asan)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Build (arm_asan)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  build_arm_coverage:
    runs-on: [self-hosted, builder-aarch64]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, style_check, fast_test, build_amd_tidy]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'QnVpbGQgKGFybV9jb3ZlcmFnZSk=') }}
    name: "Build (arm_coverage)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Build (arm_coverage)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Build (arm_coverage)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  build_arm_binary:
    runs-on: [self-hosted, builder-aarch64]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, style_check, fast_test, build_amd_tidy]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'QnVpbGQgKGFybV9iaW5hcnkp') }}
    name: "Build (arm_binary)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Build (arm_binary)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Build (arm_binary)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  build_amd_darwin:
    runs-on: [self-hosted, builder]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_debug, build_amd_release, build_amd_asan, build_amd_tsan, build_amd_msan, build_amd_ubsan, build_amd_binary, build_arm_release, build_arm_asan, build_arm_coverage, build_arm_binary]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'QnVpbGQgKGFtZF9kYXJ3aW4p') }}
    name: "Build (amd_darwin)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Build (amd_darwin)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Build (amd_darwin)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  build_arm_darwin:
    runs-on: [self-hosted, builder-aarch64]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_debug, build_amd_release, build_amd_asan, build_amd_tsan, build_amd_msan, build_amd_ubsan, build_amd_binary, build_arm_release, build_arm_asan, build_arm_coverage, build_arm_binary]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'QnVpbGQgKGFybV9kYXJ3aW4p') }}
    name: "Build (arm_darwin)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Build (arm_darwin)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Build (arm_darwin)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  build_arm_v80compat:
    runs-on: [self-hosted, builder-aarch64]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_debug, build_amd_release, build_amd_asan, build_amd_tsan, build_amd_msan, build_amd_ubsan, build_amd_binary, build_arm_release, build_arm_asan, build_arm_coverage, build_arm_binary]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'QnVpbGQgKGFybV92ODBjb21wYXQp') }}
    name: "Build (arm_v80compat)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Build (arm_v80compat)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Build (arm_v80compat)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  build_amd_freebsd:
    runs-on: [self-hosted, builder]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_debug, build_amd_release, build_amd_asan, build_amd_tsan, build_amd_msan, build_amd_ubsan, build_amd_binary, build_arm_release, build_arm_asan, build_arm_coverage, build_arm_binary]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'QnVpbGQgKGFtZF9mcmVlYnNkKQ==') }}
    name: "Build (amd_freebsd)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Build (amd_freebsd)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Build (amd_freebsd)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  build_ppc64le:
    runs-on: [self-hosted, builder-aarch64]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_debug, build_amd_release, build_amd_asan, build_amd_tsan, build_amd_msan, build_amd_ubsan, build_amd_binary, build_arm_release, build_arm_asan, build_arm_coverage, build_arm_binary]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'QnVpbGQgKHBwYzY0bGUp') }}
    name: "Build (ppc64le)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Build (ppc64le)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Build (ppc64le)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  build_amd_compat:
    runs-on: [self-hosted, builder]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_debug, build_amd_release, build_amd_asan, build_amd_tsan, build_amd_msan, build_amd_ubsan, build_amd_binary, build_arm_release, build_arm_asan, build_arm_coverage, build_arm_binary]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'QnVpbGQgKGFtZF9jb21wYXQp') }}
    name: "Build (amd_compat)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Build (amd_compat)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Build (amd_compat)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  build_amd_musl:
    runs-on: [self-hosted, builder]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_debug, build_amd_release, build_amd_asan, build_amd_tsan, build_amd_msan, build_amd_ubsan, build_amd_binary, build_arm_release, build_arm_asan, build_arm_coverage, build_arm_binary]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'QnVpbGQgKGFtZF9tdXNsKQ==') }}
    name: "Build (amd_musl)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Build (amd_musl)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Build (amd_musl)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  build_riscv64:
    runs-on: [self-hosted, builder-aarch64]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_debug, build_amd_release, build_amd_asan, build_amd_tsan, build_amd_msan, build_amd_ubsan, build_amd_binary, build_arm_release, build_arm_asan, build_arm_coverage, build_arm_binary]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'QnVpbGQgKHJpc2N2NjQp') }}
    name: "Build (riscv64)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Build (riscv64)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Build (riscv64)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  build_s390x:
    runs-on: [self-hosted, builder]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_debug, build_amd_release, build_amd_asan, build_amd_tsan, build_amd_msan, build_amd_ubsan, build_amd_binary, build_arm_release, build_arm_asan, build_arm_coverage, build_arm_binary]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'QnVpbGQgKHMzOTB4KQ==') }}
    name: "Build (s390x)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Build (s390x)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Build (s390x)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  build_loongarch64:
    runs-on: [self-hosted, builder-aarch64]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_debug, build_amd_release, build_amd_asan, build_amd_tsan, build_amd_msan, build_amd_ubsan, build_amd_binary, build_arm_release, build_arm_asan, build_arm_coverage, build_arm_binary]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'QnVpbGQgKGxvb25nYXJjaDY0KQ==') }}
    name: "Build (loongarch64)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Build (loongarch64)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Build (loongarch64)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  build_fuzzers:
    runs-on: [self-hosted, builder-aarch64]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_debug, build_amd_release, build_amd_asan, build_amd_tsan, build_amd_msan, build_amd_ubsan, build_amd_binary, build_arm_release, build_arm_asan, build_arm_coverage, build_arm_binary]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'QnVpbGQgKGZ1enplcnMp') }}
    name: "Build (fuzzers)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Build (fuzzers)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Build (fuzzers)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  unit_tests_asan:
    runs-on: [self-hosted, builder]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_asan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'VW5pdCB0ZXN0cyAoYXNhbik=') }}
    name: "Unit tests (asan)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Unit tests (asan)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Unit tests (asan)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  unit_tests_tsan:
    runs-on: [self-hosted, builder]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_tsan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'VW5pdCB0ZXN0cyAodHNhbik=') }}
    name: "Unit tests (tsan)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Unit tests (tsan)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Unit tests (tsan)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  unit_tests_msan:
    runs-on: [self-hosted, builder]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_msan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'VW5pdCB0ZXN0cyAobXNhbik=') }}
    name: "Unit tests (msan)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Unit tests (msan)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Unit tests (msan)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  unit_tests_ubsan:
    runs-on: [self-hosted, builder]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_ubsan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'VW5pdCB0ZXN0cyAodWJzYW4p') }}
    name: "Unit tests (ubsan)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Unit tests (ubsan)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Unit tests (ubsan)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  docker_server_image:
    runs-on: [self-hosted, style-checker]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_release, build_arm_release]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'RG9ja2VyIHNlcnZlciBpbWFnZQ==') }}
    name: "Docker server image"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Docker server image' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Docker server image' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  docker_keeper_image:
    runs-on: [self-hosted, style-checker]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_release, build_arm_release]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'RG9ja2VyIGtlZXBlciBpbWFnZQ==') }}
    name: "Docker keeper image"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Docker keeper image' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Docker keeper image' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  install_packages_release:
    runs-on: [self-hosted, style-checker]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_release]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'SW5zdGFsbCBwYWNrYWdlcyAocmVsZWFzZSk=') }}
    name: "Install packages (release)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Install packages (release)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Install packages (release)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  install_packages_aarch64:
    runs-on: [self-hosted, style-checker-aarch64]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_arm_release]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'SW5zdGFsbCBwYWNrYWdlcyAoYWFyY2g2NCk=') }}
    name: "Install packages (aarch64)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Install packages (aarch64)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Install packages (aarch64)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  compatibility_check_release:
    runs-on: [self-hosted, style-checker]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_release]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'Q29tcGF0aWJpbGl0eSBjaGVjayAocmVsZWFzZSk=') }}
    name: "Compatibility check (release)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Compatibility check (release)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Compatibility check (release)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  compatibility_check_aarch64:
    runs-on: [self-hosted, style-checker-aarch64]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_arm_release]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'Q29tcGF0aWJpbGl0eSBjaGVjayAoYWFyY2g2NCk=') }}
    name: "Compatibility check (aarch64)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Compatibility check (aarch64)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Compatibility check (aarch64)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  stateless_tests_amd_asan_distributed_plan_1_2:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_asan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'U3RhdGVsZXNzIHRlc3RzIChhbWRfYXNhbiwgZGlzdHJpYnV0ZWQgcGxhbiwgMS8yKQ==') }}
    name: "Stateless tests (amd_asan, distributed plan, 1/2)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Stateless tests (amd_asan, distributed plan, 1/2)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Stateless tests (amd_asan, distributed plan, 1/2)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  stateless_tests_amd_asan_distributed_plan_2_2:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_asan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'U3RhdGVsZXNzIHRlc3RzIChhbWRfYXNhbiwgZGlzdHJpYnV0ZWQgcGxhbiwgMi8yKQ==') }}
    name: "Stateless tests (amd_asan, distributed plan, 2/2)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Stateless tests (amd_asan, distributed plan, 2/2)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Stateless tests (amd_asan, distributed plan, 2/2)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  stateless_tests_amd_binary:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_binary]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'U3RhdGVsZXNzIHRlc3RzIChhbWRfYmluYXJ5KQ==') }}
    name: "Stateless tests (amd_binary)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Stateless tests (amd_binary)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Stateless tests (amd_binary)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  stateless_tests_amd_binary_old_analyzer_s3_storage_databasereplicated_1_2:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_binary]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'U3RhdGVsZXNzIHRlc3RzIChhbWRfYmluYXJ5LCBvbGQgYW5hbHl6ZXIsIHMzIHN0b3JhZ2UsIERhdGFiYXNlUmVwbGljYXRlZCwgMS8yKQ==') }}
    name: "Stateless tests (amd_binary, old analyzer, s3 storage, DatabaseReplicated, 1/2)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Stateless tests (amd_binary, old analyzer, s3 storage, DatabaseReplicated, 1/2)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Stateless tests (amd_binary, old analyzer, s3 storage, DatabaseReplicated, 1/2)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  stateless_tests_amd_binary_old_analyzer_s3_storage_databasereplicated_2_2:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_binary]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'U3RhdGVsZXNzIHRlc3RzIChhbWRfYmluYXJ5LCBvbGQgYW5hbHl6ZXIsIHMzIHN0b3JhZ2UsIERhdGFiYXNlUmVwbGljYXRlZCwgMi8yKQ==') }}
    name: "Stateless tests (amd_binary, old analyzer, s3 storage, DatabaseReplicated, 2/2)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Stateless tests (amd_binary, old analyzer, s3 storage, DatabaseReplicated, 2/2)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Stateless tests (amd_binary, old analyzer, s3 storage, DatabaseReplicated, 2/2)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  stateless_tests_amd_binary_parallelreplicas_s3_storage:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_binary]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'U3RhdGVsZXNzIHRlc3RzIChhbWRfYmluYXJ5LCBQYXJhbGxlbFJlcGxpY2FzLCBzMyBzdG9yYWdlKQ==') }}
    name: "Stateless tests (amd_binary, ParallelReplicas, s3 storage)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Stateless tests (amd_binary, ParallelReplicas, s3 storage)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Stateless tests (amd_binary, ParallelReplicas, s3 storage)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  stateless_tests_amd_debug_asyncinsert_s3_storage:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_debug]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'U3RhdGVsZXNzIHRlc3RzIChhbWRfZGVidWcsIEFzeW5jSW5zZXJ0LCBzMyBzdG9yYWdlKQ==') }}
    name: "Stateless tests (amd_debug, AsyncInsert, s3 storage)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Stateless tests (amd_debug, AsyncInsert, s3 storage)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Stateless tests (amd_debug, AsyncInsert, s3 storage)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  stateless_tests_amd_debug:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_debug]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'U3RhdGVsZXNzIHRlc3RzIChhbWRfZGVidWcp') }}
    name: "Stateless tests (amd_debug)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Stateless tests (amd_debug)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Stateless tests (amd_debug)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  stateless_tests_amd_tsan_1_3:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_tsan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'U3RhdGVsZXNzIHRlc3RzIChhbWRfdHNhbiwgMS8zKQ==') }}
    name: "Stateless tests (amd_tsan, 1/3)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Stateless tests (amd_tsan, 1/3)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Stateless tests (amd_tsan, 1/3)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  stateless_tests_amd_tsan_2_3:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_tsan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'U3RhdGVsZXNzIHRlc3RzIChhbWRfdHNhbiwgMi8zKQ==') }}
    name: "Stateless tests (amd_tsan, 2/3)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Stateless tests (amd_tsan, 2/3)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Stateless tests (amd_tsan, 2/3)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  stateless_tests_amd_tsan_3_3:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_tsan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'U3RhdGVsZXNzIHRlc3RzIChhbWRfdHNhbiwgMy8zKQ==') }}
    name: "Stateless tests (amd_tsan, 3/3)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Stateless tests (amd_tsan, 3/3)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Stateless tests (amd_tsan, 3/3)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  stateless_tests_amd_msan_1_4:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_msan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'U3RhdGVsZXNzIHRlc3RzIChhbWRfbXNhbiwgMS80KQ==') }}
    name: "Stateless tests (amd_msan, 1/4)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Stateless tests (amd_msan, 1/4)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Stateless tests (amd_msan, 1/4)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  stateless_tests_amd_msan_2_4:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_msan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'U3RhdGVsZXNzIHRlc3RzIChhbWRfbXNhbiwgMi80KQ==') }}
    name: "Stateless tests (amd_msan, 2/4)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Stateless tests (amd_msan, 2/4)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Stateless tests (amd_msan, 2/4)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  stateless_tests_amd_msan_3_4:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_msan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'U3RhdGVsZXNzIHRlc3RzIChhbWRfbXNhbiwgMy80KQ==') }}
    name: "Stateless tests (amd_msan, 3/4)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Stateless tests (amd_msan, 3/4)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Stateless tests (amd_msan, 3/4)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  stateless_tests_amd_msan_4_4:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_msan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'U3RhdGVsZXNzIHRlc3RzIChhbWRfbXNhbiwgNC80KQ==') }}
    name: "Stateless tests (amd_msan, 4/4)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Stateless tests (amd_msan, 4/4)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Stateless tests (amd_msan, 4/4)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  stateless_tests_amd_ubsan:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_ubsan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'U3RhdGVsZXNzIHRlc3RzIChhbWRfdWJzYW4p') }}
    name: "Stateless tests (amd_ubsan)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Stateless tests (amd_ubsan)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Stateless tests (amd_ubsan)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  stateless_tests_amd_debug_distributed_plan_s3_storage:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_debug]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'U3RhdGVsZXNzIHRlc3RzIChhbWRfZGVidWcsIGRpc3RyaWJ1dGVkIHBsYW4sIHMzIHN0b3JhZ2Up') }}
    name: "Stateless tests (amd_debug, distributed plan, s3 storage)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Stateless tests (amd_debug, distributed plan, s3 storage)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Stateless tests (amd_debug, distributed plan, s3 storage)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  stateless_tests_amd_tsan_s3_storage_1_3:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_tsan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'U3RhdGVsZXNzIHRlc3RzIChhbWRfdHNhbiwgczMgc3RvcmFnZSwgMS8zKQ==') }}
    name: "Stateless tests (amd_tsan, s3 storage, 1/3)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Stateless tests (amd_tsan, s3 storage, 1/3)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Stateless tests (amd_tsan, s3 storage, 1/3)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  stateless_tests_amd_tsan_s3_storage_2_3:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_tsan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'U3RhdGVsZXNzIHRlc3RzIChhbWRfdHNhbiwgczMgc3RvcmFnZSwgMi8zKQ==') }}
    name: "Stateless tests (amd_tsan, s3 storage, 2/3)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Stateless tests (amd_tsan, s3 storage, 2/3)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Stateless tests (amd_tsan, s3 storage, 2/3)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  stateless_tests_amd_tsan_s3_storage_3_3:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_tsan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'U3RhdGVsZXNzIHRlc3RzIChhbWRfdHNhbiwgczMgc3RvcmFnZSwgMy8zKQ==') }}
    name: "Stateless tests (amd_tsan, s3 storage, 3/3)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Stateless tests (amd_tsan, s3 storage, 3/3)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Stateless tests (amd_tsan, s3 storage, 3/3)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  stateless_tests_arm_binary:
    runs-on: [self-hosted, func-tester-aarch64]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_arm_binary]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'U3RhdGVsZXNzIHRlc3RzIChhcm1fYmluYXJ5KQ==') }}
    name: "Stateless tests (arm_binary)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Stateless tests (arm_binary)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Stateless tests (arm_binary)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  stateless_tests_amd_coverage_1_6:
    runs-on: [self-hosted, func-tester-aarch64]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, stateless_tests_amd_asan_distributed_plan_1_2, stateless_tests_amd_asan_distributed_plan_2_2, build_arm_coverage]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'U3RhdGVsZXNzIHRlc3RzIChhbWRfY292ZXJhZ2UsMS82KQ==') }}
    name: "Stateless tests (amd_coverage,1/6)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Stateless tests (amd_coverage,1/6)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Stateless tests (amd_coverage,1/6)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  stateless_tests_amd_coverage_2_6:
    runs-on: [self-hosted, func-tester-aarch64]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, stateless_tests_amd_asan_distributed_plan_1_2, stateless_tests_amd_asan_distributed_plan_2_2, build_arm_coverage]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'U3RhdGVsZXNzIHRlc3RzIChhbWRfY292ZXJhZ2UsMi82KQ==') }}
    name: "Stateless tests (amd_coverage,2/6)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Stateless tests (amd_coverage,2/6)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Stateless tests (amd_coverage,2/6)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  stateless_tests_amd_coverage_3_6:
    runs-on: [self-hosted, func-tester-aarch64]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, stateless_tests_amd_asan_distributed_plan_1_2, stateless_tests_amd_asan_distributed_plan_2_2, build_arm_coverage]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'U3RhdGVsZXNzIHRlc3RzIChhbWRfY292ZXJhZ2UsMy82KQ==') }}
    name: "Stateless tests (amd_coverage,3/6)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Stateless tests (amd_coverage,3/6)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Stateless tests (amd_coverage,3/6)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  stateless_tests_amd_coverage_4_6:
    runs-on: [self-hosted, func-tester-aarch64]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, stateless_tests_amd_asan_distributed_plan_1_2, stateless_tests_amd_asan_distributed_plan_2_2, build_arm_coverage]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'U3RhdGVsZXNzIHRlc3RzIChhbWRfY292ZXJhZ2UsNC82KQ==') }}
    name: "Stateless tests (amd_coverage,4/6)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Stateless tests (amd_coverage,4/6)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Stateless tests (amd_coverage,4/6)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  stateless_tests_amd_coverage_5_6:
    runs-on: [self-hosted, func-tester-aarch64]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, stateless_tests_amd_asan_distributed_plan_1_2, stateless_tests_amd_asan_distributed_plan_2_2, build_arm_coverage]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'U3RhdGVsZXNzIHRlc3RzIChhbWRfY292ZXJhZ2UsNS82KQ==') }}
    name: "Stateless tests (amd_coverage,5/6)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Stateless tests (amd_coverage,5/6)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Stateless tests (amd_coverage,5/6)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  stateless_tests_amd_coverage_6_6:
    runs-on: [self-hosted, func-tester-aarch64]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, stateless_tests_amd_asan_distributed_plan_1_2, stateless_tests_amd_asan_distributed_plan_2_2, build_arm_coverage]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'U3RhdGVsZXNzIHRlc3RzIChhbWRfY292ZXJhZ2UsNi82KQ==') }}
    name: "Stateless tests (amd_coverage,6/6)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Stateless tests (amd_coverage,6/6)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Stateless tests (amd_coverage,6/6)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  bugfix_validation_integration_tests:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, style_check, fast_test, build_amd_tidy, build_amd_debug]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'QnVnZml4IHZhbGlkYXRpb24gKGludGVncmF0aW9uIHRlc3RzKQ==') }}
    name: "Bugfix validation (integration tests)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Bugfix validation (integration tests)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Bugfix validation (integration tests)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  bugfix_validation_functional_tests:
    runs-on: [self-hosted, func-tester-aarch64]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'QnVnZml4IHZhbGlkYXRpb24gKGZ1bmN0aW9uYWwgdGVzdHMp') }}
    name: "Bugfix validation (functional tests)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Bugfix validation (functional tests)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Bugfix validation (functional tests)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  stateless_tests_amd_asan_flaky_check:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_asan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'U3RhdGVsZXNzIHRlc3RzIChhbWRfYXNhbiwgZmxha3kgY2hlY2sp') }}
    name: "Stateless tests (amd_asan, flaky check)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Stateless tests (amd_asan, flaky check)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Stateless tests (amd_asan, flaky check)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  integration_tests_asan_old_analyzer_1_6:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_asan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'SW50ZWdyYXRpb24gdGVzdHMgKGFzYW4sIG9sZCBhbmFseXplciwgMS82KQ==') }}
    name: "Integration tests (asan, old analyzer, 1/6)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Integration tests (asan, old analyzer, 1/6)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Integration tests (asan, old analyzer, 1/6)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  integration_tests_asan_old_analyzer_2_6:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_asan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'SW50ZWdyYXRpb24gdGVzdHMgKGFzYW4sIG9sZCBhbmFseXplciwgMi82KQ==') }}
    name: "Integration tests (asan, old analyzer, 2/6)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Integration tests (asan, old analyzer, 2/6)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Integration tests (asan, old analyzer, 2/6)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  integration_tests_asan_old_analyzer_3_6:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_asan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'SW50ZWdyYXRpb24gdGVzdHMgKGFzYW4sIG9sZCBhbmFseXplciwgMy82KQ==') }}
    name: "Integration tests (asan, old analyzer, 3/6)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Integration tests (asan, old analyzer, 3/6)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Integration tests (asan, old analyzer, 3/6)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  integration_tests_asan_old_analyzer_4_6:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_asan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'SW50ZWdyYXRpb24gdGVzdHMgKGFzYW4sIG9sZCBhbmFseXplciwgNC82KQ==') }}
    name: "Integration tests (asan, old analyzer, 4/6)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Integration tests (asan, old analyzer, 4/6)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Integration tests (asan, old analyzer, 4/6)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  integration_tests_asan_old_analyzer_5_6:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_asan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'SW50ZWdyYXRpb24gdGVzdHMgKGFzYW4sIG9sZCBhbmFseXplciwgNS82KQ==') }}
    name: "Integration tests (asan, old analyzer, 5/6)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Integration tests (asan, old analyzer, 5/6)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Integration tests (asan, old analyzer, 5/6)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  integration_tests_asan_old_analyzer_6_6:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_asan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'SW50ZWdyYXRpb24gdGVzdHMgKGFzYW4sIG9sZCBhbmFseXplciwgNi82KQ==') }}
    name: "Integration tests (asan, old analyzer, 6/6)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Integration tests (asan, old analyzer, 6/6)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Integration tests (asan, old analyzer, 6/6)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  integration_tests_release_1_4:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_release]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'SW50ZWdyYXRpb24gdGVzdHMgKHJlbGVhc2UsIDEvNCk=') }}
    name: "Integration tests (release, 1/4)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Integration tests (release, 1/4)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Integration tests (release, 1/4)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  integration_tests_release_2_4:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_release]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'SW50ZWdyYXRpb24gdGVzdHMgKHJlbGVhc2UsIDIvNCk=') }}
    name: "Integration tests (release, 2/4)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Integration tests (release, 2/4)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Integration tests (release, 2/4)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  integration_tests_release_3_4:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_release]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'SW50ZWdyYXRpb24gdGVzdHMgKHJlbGVhc2UsIDMvNCk=') }}
    name: "Integration tests (release, 3/4)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Integration tests (release, 3/4)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Integration tests (release, 3/4)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  integration_tests_release_4_4:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_release]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'SW50ZWdyYXRpb24gdGVzdHMgKHJlbGVhc2UsIDQvNCk=') }}
    name: "Integration tests (release, 4/4)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Integration tests (release, 4/4)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Integration tests (release, 4/4)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  integration_tests_aarch64_distributed_plan_1_4:
    runs-on: [self-hosted, func-tester-aarch64]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_arm_release]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'SW50ZWdyYXRpb24gdGVzdHMgKGFhcmNoNjQsIGRpc3RyaWJ1dGVkIHBsYW4sIDEvNCk=') }}
    name: "Integration tests (aarch64, distributed plan, 1/4)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Integration tests (aarch64, distributed plan, 1/4)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Integration tests (aarch64, distributed plan, 1/4)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  integration_tests_aarch64_distributed_plan_2_4:
    runs-on: [self-hosted, func-tester-aarch64]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_arm_release]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'SW50ZWdyYXRpb24gdGVzdHMgKGFhcmNoNjQsIGRpc3RyaWJ1dGVkIHBsYW4sIDIvNCk=') }}
    name: "Integration tests (aarch64, distributed plan, 2/4)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Integration tests (aarch64, distributed plan, 2/4)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Integration tests (aarch64, distributed plan, 2/4)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  integration_tests_aarch64_distributed_plan_3_4:
    runs-on: [self-hosted, func-tester-aarch64]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_arm_release]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'SW50ZWdyYXRpb24gdGVzdHMgKGFhcmNoNjQsIGRpc3RyaWJ1dGVkIHBsYW4sIDMvNCk=') }}
    name: "Integration tests (aarch64, distributed plan, 3/4)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Integration tests (aarch64, distributed plan, 3/4)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Integration tests (aarch64, distributed plan, 3/4)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  integration_tests_aarch64_distributed_plan_4_4:
    runs-on: [self-hosted, func-tester-aarch64]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_arm_release]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'SW50ZWdyYXRpb24gdGVzdHMgKGFhcmNoNjQsIGRpc3RyaWJ1dGVkIHBsYW4sIDQvNCk=') }}
    name: "Integration tests (aarch64, distributed plan, 4/4)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Integration tests (aarch64, distributed plan, 4/4)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Integration tests (aarch64, distributed plan, 4/4)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  integration_tests_tsan_1_6:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_tsan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'SW50ZWdyYXRpb24gdGVzdHMgKHRzYW4sIDEvNik=') }}
    name: "Integration tests (tsan, 1/6)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Integration tests (tsan, 1/6)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Integration tests (tsan, 1/6)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  integration_tests_tsan_2_6:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_tsan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'SW50ZWdyYXRpb24gdGVzdHMgKHRzYW4sIDIvNik=') }}
    name: "Integration tests (tsan, 2/6)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Integration tests (tsan, 2/6)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Integration tests (tsan, 2/6)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  integration_tests_tsan_3_6:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_tsan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'SW50ZWdyYXRpb24gdGVzdHMgKHRzYW4sIDMvNik=') }}
    name: "Integration tests (tsan, 3/6)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Integration tests (tsan, 3/6)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Integration tests (tsan, 3/6)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  integration_tests_tsan_4_6:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_tsan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'SW50ZWdyYXRpb24gdGVzdHMgKHRzYW4sIDQvNik=') }}
    name: "Integration tests (tsan, 4/6)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Integration tests (tsan, 4/6)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Integration tests (tsan, 4/6)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  integration_tests_tsan_5_6:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_tsan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'SW50ZWdyYXRpb24gdGVzdHMgKHRzYW4sIDUvNik=') }}
    name: "Integration tests (tsan, 5/6)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Integration tests (tsan, 5/6)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Integration tests (tsan, 5/6)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  integration_tests_tsan_6_6:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_tsan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'SW50ZWdyYXRpb24gdGVzdHMgKHRzYW4sIDYvNik=') }}
    name: "Integration tests (tsan, 6/6)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Integration tests (tsan, 6/6)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Integration tests (tsan, 6/6)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  integration_tests_asan_flaky_check:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_asan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'SW50ZWdyYXRpb24gdGVzdHMgKGFzYW4sIGZsYWt5IGNoZWNrKQ==') }}
    name: "Integration tests (asan, flaky check)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Integration tests (asan, flaky check)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Integration tests (asan, flaky check)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  stress_test_amd_debug:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_debug]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'U3RyZXNzIHRlc3QgKGFtZF9kZWJ1Zyk=') }}
    name: "Stress test (amd_debug)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Stress test (amd_debug)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Stress test (amd_debug)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  stress_test_amd_tsan:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_tsan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'U3RyZXNzIHRlc3QgKGFtZF90c2FuKQ==') }}
    name: "Stress test (amd_tsan)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Stress test (amd_tsan)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Stress test (amd_tsan)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  stress_test_arm_asan:
    runs-on: [self-hosted, func-tester-aarch64]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_arm_asan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'U3RyZXNzIHRlc3QgKGFybV9hc2FuKQ==') }}
    name: "Stress test (arm_asan)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Stress test (arm_asan)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Stress test (arm_asan)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  stress_test_amd_ubsan:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_ubsan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'U3RyZXNzIHRlc3QgKGFtZF91YnNhbik=') }}
    name: "Stress test (amd_ubsan)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Stress test (amd_ubsan)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Stress test (amd_ubsan)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  stress_test_amd_msan:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_msan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'U3RyZXNzIHRlc3QgKGFtZF9tc2FuKQ==') }}
    name: "Stress test (amd_msan)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Stress test (amd_msan)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Stress test (amd_msan)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  upgrade_check_arm_asan:
    runs-on: [self-hosted, func-tester-aarch64]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_arm_asan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'VXBncmFkZSBjaGVjayAoYXJtX2FzYW4p') }}
    name: "Upgrade check (arm_asan)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Upgrade check (arm_asan)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Upgrade check (arm_asan)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  upgrade_check_amd_tsan:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_tsan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'VXBncmFkZSBjaGVjayAoYW1kX3RzYW4p') }}
    name: "Upgrade check (amd_tsan)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Upgrade check (amd_tsan)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Upgrade check (amd_tsan)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  upgrade_check_amd_msan:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_msan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'VXBncmFkZSBjaGVjayAoYW1kX21zYW4p') }}
    name: "Upgrade check (amd_msan)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Upgrade check (amd_msan)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Upgrade check (amd_msan)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  upgrade_check_amd_debug:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_debug]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'VXBncmFkZSBjaGVjayAoYW1kX2RlYnVnKQ==') }}
    name: "Upgrade check (amd_debug)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Upgrade check (amd_debug)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Upgrade check (amd_debug)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  ast_fuzzer_amd_debug:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_debug]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'QVNUIGZ1enplciAoYW1kX2RlYnVnKQ==') }}
    name: "AST fuzzer (amd_debug)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'AST fuzzer (amd_debug)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'AST fuzzer (amd_debug)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  ast_fuzzer_arm_asan:
    runs-on: [self-hosted, func-tester-aarch64]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_arm_asan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'QVNUIGZ1enplciAoYXJtX2FzYW4p') }}
    name: "AST fuzzer (arm_asan)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'AST fuzzer (arm_asan)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'AST fuzzer (arm_asan)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  ast_fuzzer_amd_tsan:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_tsan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'QVNUIGZ1enplciAoYW1kX3RzYW4p') }}
    name: "AST fuzzer (amd_tsan)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'AST fuzzer (amd_tsan)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'AST fuzzer (amd_tsan)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  ast_fuzzer_amd_msan:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_msan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'QVNUIGZ1enplciAoYW1kX21zYW4p') }}
    name: "AST fuzzer (amd_msan)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'AST fuzzer (amd_msan)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'AST fuzzer (amd_msan)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  ast_fuzzer_amd_ubsan:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_ubsan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'QVNUIGZ1enplciAoYW1kX3Vic2FuKQ==') }}
    name: "AST fuzzer (amd_ubsan)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'AST fuzzer (amd_ubsan)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'AST fuzzer (amd_ubsan)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  buzzhouse_amd_debug:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_debug]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'QnV6ekhvdXNlIChhbWRfZGVidWcp') }}
    name: "BuzzHouse (amd_debug)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'BuzzHouse (amd_debug)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'BuzzHouse (amd_debug)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  buzzhouse_arm_asan:
    runs-on: [self-hosted, func-tester-aarch64]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_arm_asan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'QnV6ekhvdXNlIChhcm1fYXNhbik=') }}
    name: "BuzzHouse (arm_asan)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'BuzzHouse (arm_asan)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'BuzzHouse (arm_asan)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  buzzhouse_amd_tsan:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_tsan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'QnV6ekhvdXNlIChhbWRfdHNhbik=') }}
    name: "BuzzHouse (amd_tsan)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'BuzzHouse (amd_tsan)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'BuzzHouse (amd_tsan)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  buzzhouse_amd_msan:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_msan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'QnV6ekhvdXNlIChhbWRfbXNhbik=') }}
    name: "BuzzHouse (amd_msan)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'BuzzHouse (amd_msan)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'BuzzHouse (amd_msan)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  buzzhouse_amd_ubsan:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_ubsan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'QnV6ekhvdXNlIChhbWRfdWJzYW4p') }}
    name: "BuzzHouse (amd_ubsan)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'BuzzHouse (amd_ubsan)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'BuzzHouse (amd_ubsan)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  performance_comparison_amd_release_master_head_1_3:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_release]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'UGVyZm9ybWFuY2UgQ29tcGFyaXNvbiAoYW1kX3JlbGVhc2UsIG1hc3Rlcl9oZWFkLCAxLzMp') }}
    name: "Performance Comparison (amd_release, master_head, 1/3)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Performance Comparison (amd_release, master_head, 1/3)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Performance Comparison (amd_release, master_head, 1/3)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  performance_comparison_amd_release_master_head_2_3:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_release]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'UGVyZm9ybWFuY2UgQ29tcGFyaXNvbiAoYW1kX3JlbGVhc2UsIG1hc3Rlcl9oZWFkLCAyLzMp') }}
    name: "Performance Comparison (amd_release, master_head, 2/3)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Performance Comparison (amd_release, master_head, 2/3)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Performance Comparison (amd_release, master_head, 2/3)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  performance_comparison_amd_release_master_head_3_3:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_release]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'UGVyZm9ybWFuY2UgQ29tcGFyaXNvbiAoYW1kX3JlbGVhc2UsIG1hc3Rlcl9oZWFkLCAzLzMp') }}
    name: "Performance Comparison (amd_release, master_head, 3/3)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Performance Comparison (amd_release, master_head, 3/3)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Performance Comparison (amd_release, master_head, 3/3)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  performance_comparison_arm_release_master_head_1_3:
    runs-on: [self-hosted, func-tester-aarch64]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_arm_release]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'UGVyZm9ybWFuY2UgQ29tcGFyaXNvbiAoYXJtX3JlbGVhc2UsIG1hc3Rlcl9oZWFkLCAxLzMp') }}
    name: "Performance Comparison (arm_release, master_head, 1/3)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Performance Comparison (arm_release, master_head, 1/3)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Performance Comparison (arm_release, master_head, 1/3)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  performance_comparison_arm_release_master_head_2_3:
    runs-on: [self-hosted, func-tester-aarch64]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_arm_release]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'UGVyZm9ybWFuY2UgQ29tcGFyaXNvbiAoYXJtX3JlbGVhc2UsIG1hc3Rlcl9oZWFkLCAyLzMp') }}
    name: "Performance Comparison (arm_release, master_head, 2/3)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Performance Comparison (arm_release, master_head, 2/3)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Performance Comparison (arm_release, master_head, 2/3)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  performance_comparison_arm_release_master_head_3_3:
    runs-on: [self-hosted, func-tester-aarch64]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_arm_release]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'UGVyZm9ybWFuY2UgQ29tcGFyaXNvbiAoYXJtX3JlbGVhc2UsIG1hc3Rlcl9oZWFkLCAzLzMp') }}
    name: "Performance Comparison (arm_release, master_head, 3/3)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Performance Comparison (arm_release, master_head, 3/3)' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Performance Comparison (arm_release, master_head, 3/3)' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi

  finish_workflow:
    runs-on: [self-hosted, style-checker-aarch64]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, style_check, docs_check, fast_test, build_amd_tidy, build_arm_tidy, build_amd_debug, build_amd_release, build_amd_asan, build_amd_tsan, build_amd_msan, build_amd_ubsan, build_amd_binary, build_arm_release, build_arm_asan, build_arm_coverage, build_arm_binary, build_amd_darwin, build_arm_darwin, build_arm_v80compat, build_amd_freebsd, build_ppc64le, build_amd_compat, build_amd_musl, build_riscv64, build_s390x, build_loongarch64, build_fuzzers, unit_tests_asan, unit_tests_tsan, unit_tests_msan, unit_tests_ubsan, docker_server_image, docker_keeper_image, install_packages_release, install_packages_aarch64, compatibility_check_release, compatibility_check_aarch64, stateless_tests_amd_asan_distributed_plan_1_2, stateless_tests_amd_asan_distributed_plan_2_2, stateless_tests_amd_binary, stateless_tests_amd_binary_old_analyzer_s3_storage_databasereplicated_1_2, stateless_tests_amd_binary_old_analyzer_s3_storage_databasereplicated_2_2, stateless_tests_amd_binary_parallelreplicas_s3_storage, stateless_tests_amd_debug_asyncinsert_s3_storage, stateless_tests_amd_debug, stateless_tests_amd_tsan_1_3, stateless_tests_amd_tsan_2_3, stateless_tests_amd_tsan_3_3, stateless_tests_amd_msan_1_4, stateless_tests_amd_msan_2_4, stateless_tests_amd_msan_3_4, stateless_tests_amd_msan_4_4, stateless_tests_amd_ubsan, stateless_tests_amd_debug_distributed_plan_s3_storage, stateless_tests_amd_tsan_s3_storage_1_3, stateless_tests_amd_tsan_s3_storage_2_3, stateless_tests_amd_tsan_s3_storage_3_3, stateless_tests_arm_binary, stateless_tests_amd_coverage_1_6, stateless_tests_amd_coverage_2_6, stateless_tests_amd_coverage_3_6, stateless_tests_amd_coverage_4_6, stateless_tests_amd_coverage_5_6, stateless_tests_amd_coverage_6_6, bugfix_validation_integration_tests, bugfix_validation_functional_tests, stateless_tests_amd_asan_flaky_check, integration_tests_asan_old_analyzer_1_6, integration_tests_asan_old_analyzer_2_6, integration_tests_asan_old_analyzer_3_6, integration_tests_asan_old_analyzer_4_6, integration_tests_asan_old_analyzer_5_6, integration_tests_asan_old_analyzer_6_6, integration_tests_release_1_4, integration_tests_release_2_4, integration_tests_release_3_4, integration_tests_release_4_4, integration_tests_aarch64_distributed_plan_1_4, integration_tests_aarch64_distributed_plan_2_4, integration_tests_aarch64_distributed_plan_3_4, integration_tests_aarch64_distributed_plan_4_4, integration_tests_tsan_1_6, integration_tests_tsan_2_6, integration_tests_tsan_3_6, integration_tests_tsan_4_6, integration_tests_tsan_5_6, integration_tests_tsan_6_6, integration_tests_asan_flaky_check, stress_test_amd_debug, stress_test_amd_tsan, stress_test_arm_asan, stress_test_amd_ubsan, stress_test_amd_msan, upgrade_check_arm_asan, upgrade_check_amd_tsan, upgrade_check_amd_msan, upgrade_check_amd_debug, ast_fuzzer_amd_debug, ast_fuzzer_arm_asan, ast_fuzzer_amd_tsan, ast_fuzzer_amd_msan, ast_fuzzer_amd_ubsan, buzzhouse_amd_debug, buzzhouse_arm_asan, buzzhouse_amd_tsan, buzzhouse_amd_msan, buzzhouse_amd_ubsan, performance_comparison_amd_release_master_head_1_3, performance_comparison_amd_release_master_head_2_3, performance_comparison_amd_release_master_head_3_3, performance_comparison_arm_release_master_head_1_3, performance_comparison_arm_release_master_head_2_3, performance_comparison_arm_release_master_head_3_3]
    if: ${{ !cancelled() }}
    name: "Finish Workflow"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_pr.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Finish Workflow' --workflow "PR" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Finish Workflow' --workflow "PR" --ci |& tee ./ci/tmp/job.log
          fi
