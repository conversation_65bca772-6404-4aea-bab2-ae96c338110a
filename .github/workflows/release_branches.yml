# generated by praktika

name: ReleaseBranchCI

on:
  push:
    branches: ['2[1-9].[1-9][0-9]', '2[1-9].[1-9]']

env:
  # Force the stdout and stderr streams to be unbuffered
  PYTHONUNBUFFERED: 1
  CHECKOUT_REF: ""

# Allow updating GH commit statuses and PR comments to post an actual job reports link
permissions: write-all

jobs:

  config_workflow:
    runs-on: [self-hosted, style-checker-aarch64]
    needs: []
    name: "Config Workflow"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_releasebranchci.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Config Workflow' --workflow "ReleaseBranchCI" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Config Workflow' --workflow "ReleaseBranchCI" --ci |& tee ./ci/tmp/job.log
          fi

  dockers_build_amd:
    runs-on: [self-hosted, style-checker]
    needs: [config_workflow]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'RG9ja2VycyBCdWlsZCAoYW1kKQ==') }}
    name: "Dockers Build (amd)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_releasebranchci.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Dockers Build (amd)' --workflow "ReleaseBranchCI" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Dockers Build (amd)' --workflow "ReleaseBranchCI" --ci |& tee ./ci/tmp/job.log
          fi

  dockers_build_arm:
    runs-on: [self-hosted, style-checker-aarch64]
    needs: [config_workflow]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'RG9ja2VycyBCdWlsZCAoYXJtKQ==') }}
    name: "Dockers Build (arm)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_releasebranchci.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Dockers Build (arm)' --workflow "ReleaseBranchCI" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Dockers Build (arm)' --workflow "ReleaseBranchCI" --ci |& tee ./ci/tmp/job.log
          fi

  build_amd_debug:
    runs-on: [self-hosted, builder]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'QnVpbGQgKGFtZF9kZWJ1Zyk=') }}
    name: "Build (amd_debug)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_releasebranchci.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Build (amd_debug)' --workflow "ReleaseBranchCI" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Build (amd_debug)' --workflow "ReleaseBranchCI" --ci |& tee ./ci/tmp/job.log
          fi

  build_amd_release:
    runs-on: [self-hosted, builder]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'QnVpbGQgKGFtZF9yZWxlYXNlKQ==') }}
    name: "Build (amd_release)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_releasebranchci.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Build (amd_release)' --workflow "ReleaseBranchCI" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Build (amd_release)' --workflow "ReleaseBranchCI" --ci |& tee ./ci/tmp/job.log
          fi

  build_amd_asan:
    runs-on: [self-hosted, builder]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'QnVpbGQgKGFtZF9hc2FuKQ==') }}
    name: "Build (amd_asan)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_releasebranchci.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Build (amd_asan)' --workflow "ReleaseBranchCI" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Build (amd_asan)' --workflow "ReleaseBranchCI" --ci |& tee ./ci/tmp/job.log
          fi

  build_amd_tsan:
    runs-on: [self-hosted, builder]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'QnVpbGQgKGFtZF90c2FuKQ==') }}
    name: "Build (amd_tsan)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_releasebranchci.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Build (amd_tsan)' --workflow "ReleaseBranchCI" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Build (amd_tsan)' --workflow "ReleaseBranchCI" --ci |& tee ./ci/tmp/job.log
          fi

  build_amd_msan:
    runs-on: [self-hosted, builder]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'QnVpbGQgKGFtZF9tc2FuKQ==') }}
    name: "Build (amd_msan)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_releasebranchci.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Build (amd_msan)' --workflow "ReleaseBranchCI" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Build (amd_msan)' --workflow "ReleaseBranchCI" --ci |& tee ./ci/tmp/job.log
          fi

  build_amd_ubsan:
    runs-on: [self-hosted, builder]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'QnVpbGQgKGFtZF91YnNhbik=') }}
    name: "Build (amd_ubsan)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_releasebranchci.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Build (amd_ubsan)' --workflow "ReleaseBranchCI" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Build (amd_ubsan)' --workflow "ReleaseBranchCI" --ci |& tee ./ci/tmp/job.log
          fi

  build_arm_release:
    runs-on: [self-hosted, builder-aarch64]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'QnVpbGQgKGFybV9yZWxlYXNlKQ==') }}
    name: "Build (arm_release)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_releasebranchci.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Build (arm_release)' --workflow "ReleaseBranchCI" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Build (arm_release)' --workflow "ReleaseBranchCI" --ci |& tee ./ci/tmp/job.log
          fi

  build_arm_asan:
    runs-on: [self-hosted, builder-aarch64]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'QnVpbGQgKGFybV9hc2FuKQ==') }}
    name: "Build (arm_asan)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_releasebranchci.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Build (arm_asan)' --workflow "ReleaseBranchCI" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Build (arm_asan)' --workflow "ReleaseBranchCI" --ci |& tee ./ci/tmp/job.log
          fi

  build_amd_darwin:
    runs-on: [self-hosted, builder]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'QnVpbGQgKGFtZF9kYXJ3aW4p') }}
    name: "Build (amd_darwin)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_releasebranchci.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Build (amd_darwin)' --workflow "ReleaseBranchCI" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Build (amd_darwin)' --workflow "ReleaseBranchCI" --ci |& tee ./ci/tmp/job.log
          fi

  build_arm_darwin:
    runs-on: [self-hosted, builder-aarch64]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'QnVpbGQgKGFybV9kYXJ3aW4p') }}
    name: "Build (arm_darwin)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_releasebranchci.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Build (arm_darwin)' --workflow "ReleaseBranchCI" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Build (arm_darwin)' --workflow "ReleaseBranchCI" --ci |& tee ./ci/tmp/job.log
          fi

  docker_server_image:
    runs-on: [self-hosted, style-checker]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_release, build_arm_release]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'RG9ja2VyIHNlcnZlciBpbWFnZQ==') }}
    name: "Docker server image"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_releasebranchci.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Docker server image' --workflow "ReleaseBranchCI" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Docker server image' --workflow "ReleaseBranchCI" --ci |& tee ./ci/tmp/job.log
          fi

  docker_keeper_image:
    runs-on: [self-hosted, style-checker]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_release, build_arm_release]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'RG9ja2VyIGtlZXBlciBpbWFnZQ==') }}
    name: "Docker keeper image"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_releasebranchci.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Docker keeper image' --workflow "ReleaseBranchCI" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Docker keeper image' --workflow "ReleaseBranchCI" --ci |& tee ./ci/tmp/job.log
          fi

  install_packages_release:
    runs-on: [self-hosted, style-checker]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_release]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'SW5zdGFsbCBwYWNrYWdlcyAocmVsZWFzZSk=') }}
    name: "Install packages (release)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_releasebranchci.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Install packages (release)' --workflow "ReleaseBranchCI" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Install packages (release)' --workflow "ReleaseBranchCI" --ci |& tee ./ci/tmp/job.log
          fi

  install_packages_aarch64:
    runs-on: [self-hosted, style-checker-aarch64]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_arm_release]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'SW5zdGFsbCBwYWNrYWdlcyAoYWFyY2g2NCk=') }}
    name: "Install packages (aarch64)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_releasebranchci.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Install packages (aarch64)' --workflow "ReleaseBranchCI" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Install packages (aarch64)' --workflow "ReleaseBranchCI" --ci |& tee ./ci/tmp/job.log
          fi

  integration_tests_asan_1_4:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_asan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'SW50ZWdyYXRpb24gdGVzdHMgKGFzYW4sIDEvNCk=') }}
    name: "Integration tests (asan, 1/4)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_releasebranchci.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Integration tests (asan, 1/4)' --workflow "ReleaseBranchCI" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Integration tests (asan, 1/4)' --workflow "ReleaseBranchCI" --ci |& tee ./ci/tmp/job.log
          fi

  integration_tests_asan_2_4:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_asan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'SW50ZWdyYXRpb24gdGVzdHMgKGFzYW4sIDIvNCk=') }}
    name: "Integration tests (asan, 2/4)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_releasebranchci.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Integration tests (asan, 2/4)' --workflow "ReleaseBranchCI" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Integration tests (asan, 2/4)' --workflow "ReleaseBranchCI" --ci |& tee ./ci/tmp/job.log
          fi

  integration_tests_asan_3_4:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_asan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'SW50ZWdyYXRpb24gdGVzdHMgKGFzYW4sIDMvNCk=') }}
    name: "Integration tests (asan, 3/4)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_releasebranchci.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Integration tests (asan, 3/4)' --workflow "ReleaseBranchCI" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Integration tests (asan, 3/4)' --workflow "ReleaseBranchCI" --ci |& tee ./ci/tmp/job.log
          fi

  integration_tests_asan_4_4:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_asan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'SW50ZWdyYXRpb24gdGVzdHMgKGFzYW4sIDQvNCk=') }}
    name: "Integration tests (asan, 4/4)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_releasebranchci.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Integration tests (asan, 4/4)' --workflow "ReleaseBranchCI" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Integration tests (asan, 4/4)' --workflow "ReleaseBranchCI" --ci |& tee ./ci/tmp/job.log
          fi

  integration_tests_asan_old_analyzer_1_6:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_asan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'SW50ZWdyYXRpb24gdGVzdHMgKGFzYW4sIG9sZCBhbmFseXplciwgMS82KQ==') }}
    name: "Integration tests (asan, old analyzer, 1/6)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_releasebranchci.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Integration tests (asan, old analyzer, 1/6)' --workflow "ReleaseBranchCI" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Integration tests (asan, old analyzer, 1/6)' --workflow "ReleaseBranchCI" --ci |& tee ./ci/tmp/job.log
          fi

  integration_tests_asan_old_analyzer_2_6:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_asan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'SW50ZWdyYXRpb24gdGVzdHMgKGFzYW4sIG9sZCBhbmFseXplciwgMi82KQ==') }}
    name: "Integration tests (asan, old analyzer, 2/6)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_releasebranchci.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Integration tests (asan, old analyzer, 2/6)' --workflow "ReleaseBranchCI" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Integration tests (asan, old analyzer, 2/6)' --workflow "ReleaseBranchCI" --ci |& tee ./ci/tmp/job.log
          fi

  integration_tests_asan_old_analyzer_3_6:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_asan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'SW50ZWdyYXRpb24gdGVzdHMgKGFzYW4sIG9sZCBhbmFseXplciwgMy82KQ==') }}
    name: "Integration tests (asan, old analyzer, 3/6)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_releasebranchci.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Integration tests (asan, old analyzer, 3/6)' --workflow "ReleaseBranchCI" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Integration tests (asan, old analyzer, 3/6)' --workflow "ReleaseBranchCI" --ci |& tee ./ci/tmp/job.log
          fi

  integration_tests_asan_old_analyzer_4_6:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_asan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'SW50ZWdyYXRpb24gdGVzdHMgKGFzYW4sIG9sZCBhbmFseXplciwgNC82KQ==') }}
    name: "Integration tests (asan, old analyzer, 4/6)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_releasebranchci.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Integration tests (asan, old analyzer, 4/6)' --workflow "ReleaseBranchCI" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Integration tests (asan, old analyzer, 4/6)' --workflow "ReleaseBranchCI" --ci |& tee ./ci/tmp/job.log
          fi

  integration_tests_asan_old_analyzer_5_6:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_asan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'SW50ZWdyYXRpb24gdGVzdHMgKGFzYW4sIG9sZCBhbmFseXplciwgNS82KQ==') }}
    name: "Integration tests (asan, old analyzer, 5/6)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_releasebranchci.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Integration tests (asan, old analyzer, 5/6)' --workflow "ReleaseBranchCI" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Integration tests (asan, old analyzer, 5/6)' --workflow "ReleaseBranchCI" --ci |& tee ./ci/tmp/job.log
          fi

  integration_tests_asan_old_analyzer_6_6:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_asan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'SW50ZWdyYXRpb24gdGVzdHMgKGFzYW4sIG9sZCBhbmFseXplciwgNi82KQ==') }}
    name: "Integration tests (asan, old analyzer, 6/6)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_releasebranchci.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Integration tests (asan, old analyzer, 6/6)' --workflow "ReleaseBranchCI" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Integration tests (asan, old analyzer, 6/6)' --workflow "ReleaseBranchCI" --ci |& tee ./ci/tmp/job.log
          fi

  integration_tests_release_1_4:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_release]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'SW50ZWdyYXRpb24gdGVzdHMgKHJlbGVhc2UsIDEvNCk=') }}
    name: "Integration tests (release, 1/4)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_releasebranchci.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Integration tests (release, 1/4)' --workflow "ReleaseBranchCI" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Integration tests (release, 1/4)' --workflow "ReleaseBranchCI" --ci |& tee ./ci/tmp/job.log
          fi

  integration_tests_release_2_4:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_release]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'SW50ZWdyYXRpb24gdGVzdHMgKHJlbGVhc2UsIDIvNCk=') }}
    name: "Integration tests (release, 2/4)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_releasebranchci.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Integration tests (release, 2/4)' --workflow "ReleaseBranchCI" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Integration tests (release, 2/4)' --workflow "ReleaseBranchCI" --ci |& tee ./ci/tmp/job.log
          fi

  integration_tests_release_3_4:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_release]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'SW50ZWdyYXRpb24gdGVzdHMgKHJlbGVhc2UsIDMvNCk=') }}
    name: "Integration tests (release, 3/4)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_releasebranchci.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Integration tests (release, 3/4)' --workflow "ReleaseBranchCI" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Integration tests (release, 3/4)' --workflow "ReleaseBranchCI" --ci |& tee ./ci/tmp/job.log
          fi

  integration_tests_release_4_4:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_release]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'SW50ZWdyYXRpb24gdGVzdHMgKHJlbGVhc2UsIDQvNCk=') }}
    name: "Integration tests (release, 4/4)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_releasebranchci.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Integration tests (release, 4/4)' --workflow "ReleaseBranchCI" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Integration tests (release, 4/4)' --workflow "ReleaseBranchCI" --ci |& tee ./ci/tmp/job.log
          fi

  integration_tests_tsan_1_6:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_tsan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'SW50ZWdyYXRpb24gdGVzdHMgKHRzYW4sIDEvNik=') }}
    name: "Integration tests (tsan, 1/6)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_releasebranchci.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Integration tests (tsan, 1/6)' --workflow "ReleaseBranchCI" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Integration tests (tsan, 1/6)' --workflow "ReleaseBranchCI" --ci |& tee ./ci/tmp/job.log
          fi

  integration_tests_tsan_2_6:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_tsan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'SW50ZWdyYXRpb24gdGVzdHMgKHRzYW4sIDIvNik=') }}
    name: "Integration tests (tsan, 2/6)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_releasebranchci.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Integration tests (tsan, 2/6)' --workflow "ReleaseBranchCI" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Integration tests (tsan, 2/6)' --workflow "ReleaseBranchCI" --ci |& tee ./ci/tmp/job.log
          fi

  integration_tests_tsan_3_6:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_tsan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'SW50ZWdyYXRpb24gdGVzdHMgKHRzYW4sIDMvNik=') }}
    name: "Integration tests (tsan, 3/6)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_releasebranchci.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Integration tests (tsan, 3/6)' --workflow "ReleaseBranchCI" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Integration tests (tsan, 3/6)' --workflow "ReleaseBranchCI" --ci |& tee ./ci/tmp/job.log
          fi

  integration_tests_tsan_4_6:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_tsan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'SW50ZWdyYXRpb24gdGVzdHMgKHRzYW4sIDQvNik=') }}
    name: "Integration tests (tsan, 4/6)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_releasebranchci.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Integration tests (tsan, 4/6)' --workflow "ReleaseBranchCI" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Integration tests (tsan, 4/6)' --workflow "ReleaseBranchCI" --ci |& tee ./ci/tmp/job.log
          fi

  integration_tests_tsan_5_6:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_tsan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'SW50ZWdyYXRpb24gdGVzdHMgKHRzYW4sIDUvNik=') }}
    name: "Integration tests (tsan, 5/6)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_releasebranchci.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Integration tests (tsan, 5/6)' --workflow "ReleaseBranchCI" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Integration tests (tsan, 5/6)' --workflow "ReleaseBranchCI" --ci |& tee ./ci/tmp/job.log
          fi

  integration_tests_tsan_6_6:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_tsan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'SW50ZWdyYXRpb24gdGVzdHMgKHRzYW4sIDYvNik=') }}
    name: "Integration tests (tsan, 6/6)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_releasebranchci.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Integration tests (tsan, 6/6)' --workflow "ReleaseBranchCI" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Integration tests (tsan, 6/6)' --workflow "ReleaseBranchCI" --ci |& tee ./ci/tmp/job.log
          fi

  stress_test_amd_debug:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_debug]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'U3RyZXNzIHRlc3QgKGFtZF9kZWJ1Zyk=') }}
    name: "Stress test (amd_debug)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_releasebranchci.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Stress test (amd_debug)' --workflow "ReleaseBranchCI" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Stress test (amd_debug)' --workflow "ReleaseBranchCI" --ci |& tee ./ci/tmp/job.log
          fi

  stress_test_amd_tsan:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_tsan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'U3RyZXNzIHRlc3QgKGFtZF90c2FuKQ==') }}
    name: "Stress test (amd_tsan)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_releasebranchci.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Stress test (amd_tsan)' --workflow "ReleaseBranchCI" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Stress test (amd_tsan)' --workflow "ReleaseBranchCI" --ci |& tee ./ci/tmp/job.log
          fi

  stress_test_arm_asan:
    runs-on: [self-hosted, func-tester-aarch64]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_arm_asan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'U3RyZXNzIHRlc3QgKGFybV9hc2FuKQ==') }}
    name: "Stress test (arm_asan)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_releasebranchci.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Stress test (arm_asan)' --workflow "ReleaseBranchCI" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Stress test (arm_asan)' --workflow "ReleaseBranchCI" --ci |& tee ./ci/tmp/job.log
          fi

  stress_test_amd_ubsan:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_ubsan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'U3RyZXNzIHRlc3QgKGFtZF91YnNhbik=') }}
    name: "Stress test (amd_ubsan)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_releasebranchci.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Stress test (amd_ubsan)' --workflow "ReleaseBranchCI" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Stress test (amd_ubsan)' --workflow "ReleaseBranchCI" --ci |& tee ./ci/tmp/job.log
          fi

  stress_test_amd_msan:
    runs-on: [self-hosted, func-tester]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_amd_msan]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'U3RyZXNzIHRlc3QgKGFtZF9tc2FuKQ==') }}
    name: "Stress test (amd_msan)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_releasebranchci.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Stress test (amd_msan)' --workflow "ReleaseBranchCI" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Stress test (amd_msan)' --workflow "ReleaseBranchCI" --ci |& tee ./ci/tmp/job.log
          fi
