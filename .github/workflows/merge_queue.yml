# generated by praktika

name: MergeQueueCI

on:
  merge_group:

env:
  # Force the stdout and stderr streams to be unbuffered
  PYTHONUNBUFFERED: 1
  CHECKOUT_REF: ""


jobs:

  config_workflow:
    runs-on: [self-hosted, style-checker-aarch64]
    needs: []
    name: "Config Workflow"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_mergequeueci.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Config Workflow' --workflow "MergeQueueCI" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Config Workflow' --workflow "MergeQueueCI" --ci |& tee ./ci/tmp/job.log
          fi

  dockers_build_amd:
    runs-on: [self-hosted, style-checker]
    needs: [config_workflow]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'RG9ja2VycyBCdWlsZCAoYW1kKQ==') }}
    name: "Dockers Build (amd)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_mergequeueci.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Dockers Build (amd)' --workflow "MergeQueueCI" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Dockers Build (amd)' --workflow "MergeQueueCI" --ci |& tee ./ci/tmp/job.log
          fi

  dockers_build_arm:
    runs-on: [self-hosted, style-checker-aarch64]
    needs: [config_workflow]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'RG9ja2VycyBCdWlsZCAoYXJtKQ==') }}
    name: "Dockers Build (arm)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_mergequeueci.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Dockers Build (arm)' --workflow "MergeQueueCI" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Dockers Build (arm)' --workflow "MergeQueueCI" --ci |& tee ./ci/tmp/job.log
          fi

  style_check:
    runs-on: [self-hosted, style-checker-aarch64]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'U3R5bGUgY2hlY2s=') }}
    name: "Style check"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_mergequeueci.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Style check' --workflow "MergeQueueCI" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Style check' --workflow "MergeQueueCI" --ci |& tee ./ci/tmp/job.log
          fi

  fast_test:
    runs-on: [self-hosted, builder]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'RmFzdCB0ZXN0') }}
    name: "Fast test"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_mergequeueci.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Fast test' --workflow "MergeQueueCI" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Fast test' --workflow "MergeQueueCI" --ci |& tee ./ci/tmp/job.log
          fi

  build_amd_binary:
    runs-on: [self-hosted, builder]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'QnVpbGQgKGFtZF9iaW5hcnkp') }}
    name: "Build (amd_binary)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_mergequeueci.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Build (amd_binary)' --workflow "MergeQueueCI" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Build (amd_binary)' --workflow "MergeQueueCI" --ci |& tee ./ci/tmp/job.log
          fi

  finish_workflow:
    runs-on: [self-hosted, style-checker-aarch64]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, style_check, fast_test, build_amd_binary]
    if: ${{ !cancelled() }}
    name: "Finish Workflow"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_mergequeueci.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Finish Workflow' --workflow "MergeQueueCI" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Finish Workflow' --workflow "MergeQueueCI" --ci |& tee ./ci/tmp/job.log
          fi
