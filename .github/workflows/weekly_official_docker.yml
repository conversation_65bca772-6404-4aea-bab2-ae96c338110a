name: WeeklyOfficialDocker

env:
  # Force the stdout and stderr streams to be unbuffered
  PYTHONUNBUFFERED: 1

"on":
  schedule:
    # Fridays 09:55 UTC
    - cron: '55 9 * * 5'
  workflow_dispatch:
    inputs:
      dry-run:
        description: 'Dry run'
        required: false
        default: true
        type: boolean

concurrency:
  group: official-docker-library

jobs:
  UpdateOfficialDocker:
    runs-on: [self-hosted, style-checker-aarch64]
    steps:
      - name: Check out repository code
        uses: ClickHouse/checkout@v1
        with:
          clear-repository: true
          fetch-depth: 0 # to get all tags and their content
      - name: Debug Info
        uses: ./.github/actions/debug
      - name: Set envs
        run: |
          cat >> "$GITHUB_ENV" << 'EOF'
          DRY_RUN=${{ github.event.inputs.dry-run == 'true' && '--dry-run' || '' }}
          ROBOT_CLICKHOUSE_SSH_KEY<<RCSK
          ${{secrets.ROBOT_CLICKHOUSE_SSH_KEY}}
          RCSK
          EOF
      - name: Update official docker repos
        run: |
          cd "$GITHUB_WORKSPACE/tests/ci"
          python3 official_docker_update.py -vv $DRY_RUN
