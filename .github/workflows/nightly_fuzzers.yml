# generated by praktika

name: NightlyFuzzers
on:
  schedule:
    - cron: 13 3 * * *
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}

env:
  PYTHONUNBUFFERED: 1
  CHECKOUT_REF: ""

jobs:

  config_workflow:
    runs-on: [self-hosted, style-checker-aarch64]
    needs: []
    name: "Config Workflow"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_nightlyfuzzers.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Config Workflow' --workflow "NightlyFuzzers" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Config Workflow' --workflow "NightlyFuzzers" --ci |& tee ./ci/tmp/job.log
          fi

  dockers_build_amd:
    runs-on: [self-hosted, style-checker]
    needs: [config_workflow]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'RG9ja2VycyBCdWlsZCAoYW1kKQ==') }}
    name: "Dockers Build (amd)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_nightlyfuzzers.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Dockers Build (amd)' --workflow "NightlyFuzzers" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Dockers Build (amd)' --workflow "NightlyFuzzers" --ci |& tee ./ci/tmp/job.log
          fi

  dockers_build_arm:
    runs-on: [self-hosted, style-checker-aarch64]
    needs: [config_workflow]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'RG9ja2VycyBCdWlsZCAoYXJtKQ==') }}
    name: "Dockers Build (arm)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_nightlyfuzzers.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Dockers Build (arm)' --workflow "NightlyFuzzers" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Dockers Build (arm)' --workflow "NightlyFuzzers" --ci |& tee ./ci/tmp/job.log
          fi

  build_fuzzers:
    runs-on: [self-hosted, builder-aarch64]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'QnVpbGQgKGZ1enplcnMp') }}
    name: "Build (fuzzers)"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_nightlyfuzzers.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'Build (fuzzers)' --workflow "NightlyFuzzers" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'Build (fuzzers)' --workflow "NightlyFuzzers" --ci |& tee ./ci/tmp/job.log
          fi

  libfuzzer_tests:
    runs-on: [self-hosted, func-tester-aarch64]
    needs: [config_workflow, dockers_build_amd, dockers_build_arm, build_fuzzers]
    if: ${{ !failure() && !cancelled() && !contains(fromJson(needs.config_workflow.outputs.data).cache_success_base64, 'bGliRnV6emVyIHRlc3Rz') }}
    name: "libFuzzer tests"
    outputs:
      data: ${{ steps.run.outputs.DATA }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ env.CHECKOUT_REF }}

      - name: Prepare env script
        run: |
          rm -rf ./ci/tmp ./ci/tmp ./ci/tmp
          mkdir -p ./ci/tmp ./ci/tmp ./ci/tmp
          cat > ./ci/tmp/praktika_setup_env.sh << 'ENV_SETUP_SCRIPT_EOF'
          export PYTHONPATH=./ci:.:
          cat > ./ci/tmp/workflow_config_nightlyfuzzers.json << 'EOF'
          ${{ needs.config_workflow.outputs.data }}
          EOF
          cat > ./ci/tmp/workflow_status.json << 'EOF'
          ${{ toJson(needs) }}
          EOF
          ENV_SETUP_SCRIPT_EOF

      - name: Run
        id: run
        run: |
          . ./ci/tmp/praktika_setup_env.sh
          set -o pipefail
          if command -v ts &> /dev/null; then
            python3 -m praktika run 'libFuzzer tests' --workflow "NightlyFuzzers" --ci |& ts '[%Y-%m-%d %H:%M:%S]' | tee ./ci/tmp/job.log
          else
            python3 -m praktika run 'libFuzzer tests' --workflow "NightlyFuzzers" --ci |& tee ./ci/tmp/job.log
          fi
