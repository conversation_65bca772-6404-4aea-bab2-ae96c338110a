# This variables autochanged by tests/ci/version_helper.py:

# NOTE: VERSION_REVISION has nothing common with DBMS_TCP_PROTOCOL_VERSION,
# only DBMS_TCP_PROTOCOL_VERSION should be incremented on protocol changes.
SET(VERSION_REVISION 54500)
SET(VERSION_MAJOR 25)
SET(VERSION_MINOR 7)
SET(VERSION_PATCH 1)
SET(VERSION_GITHASH c5372bebfd3a1cac809291ef38205d7673a702dd)
SET(VERSION_DESCRIBE v25.7.1.1-testing)
SET(VERSION_STRING 25.7.1.1)
# end of autochange
