events {
	use epoll;
	worker_connections 128;
}

http {
	# Docker DNS resolver
	resolver 127.0.0.11;

	map $http_x_forwarded_proto $redirect_scheme {
		default $scheme;
		https https;
	}

	# Redirect proxy
	server {
		listen 8080;
		server_name proxy1 proxy2;

		# To allow special characters in headers
		ignore_invalid_headers off;

		return 307 $redirect_scheme://${S3_HOST}:${S3_PORT}$request_uri;
	}

	# Reverse proxy
	server {
		listen 80;
		listen 443 ssl;
		server_name proxy1 proxy2;

		ssl_certificate /etc/ssl/certs/server.crt;
		ssl_certificate_key /etc/ssl/certs/server.key;

		# To allow special characters in headers
		ignore_invalid_headers off;
		# Allow any size file to be uploaded.
		# Set to a value such as 1000m; to restrict file size to a specific value
		client_max_body_size 0;
		# To disable buffering
		proxy_buffering off;

		location / {
			proxy_set_header X-Real-IP $remote_addr;
			proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
			proxy_set_header X-Forwarded-Proto $scheme;
			proxy_set_header Host $http_host;

			proxy_connect_timeout 300;
			# Default is HTTP/1, keepalive is only enabled in HTTP/1.1
			proxy_http_version 1.1;
			proxy_set_header Connection "";
			chunked_transfer_encoding off;

			proxy_pass $scheme://${S3_HOST}:${S3_PORT};
			proxy_ssl_verify off;
		}
	}
}
