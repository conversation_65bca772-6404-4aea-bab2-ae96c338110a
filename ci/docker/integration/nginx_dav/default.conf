server {
    listen 80;

    #root /usr/share/nginx/test.com;
    index index.html index.htm;

    server_name test.com localhost;

    location / {
        expires max;
        root /usr/share/nginx/files;
        client_max_body_size 20m;
        client_body_temp_path /usr/share/nginx/tmp;
        dav_methods PUT; # Allowed methods, only PUT is necessary

        create_full_put_path on; # nginx automatically creates nested directories
        dav_access user:rw group:r all:r; # access permissions for files

        limit_except GET {
            allow all;
        }
    }

    error_page  405     =200 $uri;
}
