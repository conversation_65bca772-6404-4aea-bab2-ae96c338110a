import re
from pathlib import Path

from praktika.info import Info
from praktika.utils import Shell


class CHVersion:
    FILE_WITH_VERSION_PATH = "./cmake/autogenerated_versions.txt"
    VERSIONS_TEMPLATE = """\
# Autogenerated code

SET(VERSION_REVISION {revision})
SET(VERSION_MAJOR {major})
SET(VERSION_MINOR {minor})
SET(VERSION_PATCH {patch})
SET(VERSION_GITHASH {githash})
SET(VERSION_DESCRIBE {describe})
SET(VERSION_STRING {string})
"""

    @classmethod
    def get_release_version_as_dict(cls):
        versions = {}
        for line in (
            Path(cls.FILE_WITH_VERSION_PATH).read_text(encoding="utf-8").splitlines()
        ):
            line = line.strip()
            if not line.startswith("SET("):
                continue

            name, value = line[4:-1].split(maxsplit=1)
            name = name.removeprefix("VERSION_").lower()
            if name in ("major", "minor", "patch"):
                value = int(value)
            versions[name] = value

        result = {
            "major": versions["major"],
            "minor": versions["minor"],
            "patch": versions["patch"],
            "revision": versions["revision"],
            "githash": versions["githash"],
            "describe": versions["describe"],
            "string": versions["string"],
        }
        return result

    @classmethod
    def get_current_version_as_dict(cls):
        version = cls.get_release_version_as_dict()
        info = Info()
        try:
            tweak = int(
                Shell.get_output(
                    f"git rev-list --count {version['githash']}..HEAD", verbose=True
                )
            )
        except ValueError:
            # Shallow checkout
            tweak = 1
        version_type = "testing"
        if info.pr_number == 0 and bool(
            re.match(r"^\d{2}\.\d+$", info.git_branch.removeprefix("release/"))
        ):
            if version["minor"] % 5 == 3:
                version_type = "lts"
            else:
                version_type = "stable"
        version_string = (
            f'{version["major"]}.{version["minor"]}.{version["patch"]}.{tweak}'
        )
        version_description = f"v{version_string}-{version_type}"
        version["githash"] = info.sha
        version["tweak"] = tweak
        version["describe"] = version_description
        version["string"] = version_string
        return version

    @classmethod
    def get_version(cls):
        return cls.get_current_version_as_dict()["string"]

    @classmethod
    def set_binary_version(cls, version_dict=None):
        version_dict = version_dict or cls.get_current_version_as_dict()
        Path(cls.FILE_WITH_VERSION_PATH).write_text(
            cls.VERSIONS_TEMPLATE.format_map(version_dict), encoding="utf-8"
        )

    @classmethod
    def get_release_sha(cls):
        return cls.get_release_version_as_dict()["githash"]

    @classmethod
    def store_version_data_in_ci_pipeline(cls):
        Info().store_custom_data("version", cls.get_current_version_as_dict())
