<clickhouse>
    <profiles>
        <default>
            <max_execution_time>10</max_execution_time>
            <max_memory_usage>10G</max_memory_usage>

            <!--
                Otherwise we will get the TOO_MANY_SIMULTANEOUS_QUERIES errors,
                they are ok, but complicate debugging.
            -->
            <table_function_remote_max_addresses>200</table_function_remote_max_addresses>

            <!--
                Don't let the fuzzer change this setting (I've actually seen it
                do this before).
            -->
            <constraints>
                <max_execution_time>
                    <max>10</max>
                </max_execution_time>

                <max_memory_usage>
                    <max>5G</max>
                </max_memory_usage>

                <table_function_remote_max_addresses>
                    <max>200</max>
                </table_function_remote_max_addresses>

                <!-- Don't waste cycles testing the old interpreter. Spend time in the new analyzer instead -->
                <enable_analyzer>
                    <readonly/>
                </enable_analyzer>

                <!-- This feature is broken, deprecated and will be removed. We don't want more reports about it -->
                <allow_experimental_object_type>
                    <readonly/>
                </allow_experimental_object_type>

                <!-- Prevent stack overflow -->
                <max_ast_depth>
                    <readonly/>
                </max_ast_depth>
            </constraints>
        </default>
    </profiles>
</clickhouse>
