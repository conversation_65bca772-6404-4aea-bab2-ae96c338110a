#!/usr/bin/env bash

# The mypy supports pyproject.toml, but unfortunately it doesn't support it recursively
# https://github.com/python/mypy/issues/10613
#
# Unless it's done, mypy only runs against tests/ci
# Let's leave here a room for improvement and redo it when mypy will test anything else

GIT_ROOT=$(git rev-parse --show-cdup)
GIT_ROOT=${GIT_ROOT:-.}
CONFIG="$GIT_ROOT/tests/ci/.mypy.ini"
DIRS=("$GIT_ROOT/tests/ci/" "$GIT_ROOT/tests/ci/"*/)
tmp=$(mktemp)

for dir in "${DIRS[@]}"; do
  if ! compgen -G "$dir"/*.py > /dev/null; then
    continue
  fi
  if ! mypy --config-file="$CONFIG" --sqlite-cache $(find "$dir" -maxdepth 1 -name "*.py" | grep -v "test_")  > "$tmp" 2>&1; then
    echo "Errors while processing $dir":
    cat "$tmp"
  fi
done

rm -rf "$tmp"
