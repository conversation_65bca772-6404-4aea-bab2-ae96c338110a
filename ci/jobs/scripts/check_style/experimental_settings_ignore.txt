allow_aggregate_partitions_independently
allow_archive_path_syntax
allow_asynchronous_read_from_io_pool_for_merge_tree
allow_changing_replica_until_first_data_packet
allow_custom_error_code_in_throwif
allow_ddl
allow_deprecated_database_ordinary
allow_deprecated_snowflake_conversion_functions
allow_distributed_ddl
allow_drop_detached
allow_execute_multiif_columnar
allow_experimental_alter_materialized_view_structure
allow_experimental_analyzer
allow_experimental_annoy_index
allow_experimental_database_atomic
allow_experimental_database_materialized_mysql
allow_experimental_database_materialized_postgresql
allow_experimental_database_replicated
allow_experimental_join_condition
allow_experimental_join_right_table_sorting
allow_experimental_kafka_offsets_storage_in_keeper
allow_experimental_kusto_dialect
allow_experimental_lightweight_delete
allow_experimental_materialized_postgresql_table
allow_experimental_parallel_reading_from_replicas
allow_experimental_projection_optimization
allow_experimental_prql_dialect
allow_experimental_query_cache
allow_experimental_query_deduplication
allow_experimental_refreshable_materialized_view
allow_experimental_shared_merge_tree
allow_experimental_statistic
allow_experimental_statistics
allow_experimental_time_series_table
allow_experimental_ts_to_grid_aggregate_function
allow_experimental_undrop_table_query
allow_experimental_usearch_index
allow_get_client_http_header
allow_introspection_functions
allow_materialized_view_with_bad_select
allow_named_collection_override_by_default
allow_non_metadata_alters
allow_nonconst_timezone_arguments
allow_nondeterministic_mutations
allow_nondeterministic_optimize_skip_unused_shards
allow_prefetched_read_pool_for_local_filesystem
allow_prefetched_read_pool_for_remote_filesystem
allow_push_predicate_when_subquery_contains_with
allow_push_predicate_ast_for_distributed_subqueries
allow_settings_after_format_in_insert
allow_statistic_optimize
allow_statistics_optimize
allow_unrestricted_reads_from_keeper
allow_reorder_prewhere_conditions
allow_general_join_planning
